import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { BookingProvider } from './context/BookingContext';
import Header from './components/Header';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';

// Performance and Accessibility utilities
import { initPerformanceOptimizations, measureWebVitals } from './utils/performance';
import { initAccessibilityFeatures } from './utils/accessibility';

// Pages
import HomePage from './pages/HomePage';
import SearchResultsPage from './pages/SearchResultsPage';
import FAQPage from './pages/FAQPage';
import HowItWorksPage from './pages/HowItWorksPage';
import UseCasesPage from './pages/UseCasesPage';
import ContactPage from './pages/ContactPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import TermsOfServicePage from './pages/TermsOfServicePage';
import RefundPolicyPage from './pages/RefundPolicyPage';
import BlogPage from './pages/BlogPage';
import BlogPostPage from './pages/BlogPostPage';
import SimpleBlogPostPage from './pages/SimpleBlogPostPage';
import HybridSEOBlogPostPage from './pages/HybridSEOBlogPostPage';
import EditorialBlogDemo from './pages/EditorialBlogDemo';
import CheckoutPageNuclear from './pages/CheckoutPageNuclear';
import CheckoutDebugTest from './pages/CheckoutDebugTest';
import PassengerDetailsDemo from './pages/PassengerDetailsDemo';
import CheckoutSimpleFixed from './pages/CheckoutSimpleFixed';
import CheckoutStepByStep from './pages/CheckoutStepByStep';
import CheckoutMinimal from './pages/CheckoutMinimal';
import CheckoutErrorBoundary from './components/CheckoutErrorBoundary';
import CheckoutPageTest from './pages/CheckoutPageTest';
import CheckoutPageSimpleTest from './pages/CheckoutPageSimpleTest';
import CheckoutPageBookingTest from './pages/CheckoutPageBookingTest';
import CheckoutPageFixed from './pages/CheckoutPageFixed';
import CheckoutDemo from './pages/CheckoutDemo';
import CheckoutDemoOneWay from './pages/CheckoutDemoOneWay';
import CheckoutDemoReturn from './pages/CheckoutDemoReturn';

import CheckoutTestPage from './pages/CheckoutTestPage';
import FlightTicketDemo from './pages/FlightTicketDemo';
import SuccessPage from './pages/SuccessPage';

// Debug/Test Pages (conditionally rendered)
import DebugPage from './pages/DebugPage';
import CheckoutDebug from './pages/CheckoutDebug';
import CheckoutDebugPage from './pages/CheckoutDebugPage';
import CheckoutFlowTest from './pages/CheckoutFlowTest';
import CheckoutPageRobustFixed from './pages/CheckoutPageRobustFixed';
import APITestPage from './pages/APITestPage';
import CheckoutPricingTest from './test/CheckoutPricingTest';
import SEOTestPage from './pages/SEOTestPage';

function App() {
  // Initialize performance and accessibility features
  useEffect(() => {
    // Initialize performance optimizations
    initPerformanceOptimizations();

    // Initialize accessibility features (skip links disabled to prevent navigation tooltip)
    // initAccessibilityFeatures();

    // Start measuring web vitals
    measureWebVitals();

    // Add main content landmark
    const main = document.querySelector('main');
    if (main && !main.id) {
      main.id = 'main-content';
    }
  }, []);

  return (
    <BookingProvider>
      <Router>
        <div className="min-h-screen bg-neutral-50">
          <ScrollToTop />
          <Header />

          <main id="main-content" className="flex-grow" role="main">
            <Routes>
              {/* Main Pages */}
              <Route path="/" element={<HomePage />} />
              <Route path="/search" element={<SearchResultsPage />} />
              <Route path="/faq" element={<FAQPage />} />
              <Route path="/how-it-works" element={<HowItWorksPage />} />
              <Route path="/use-cases" element={<UseCasesPage />} />
              <Route path="/contact" element={<ContactPage />} />
              <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
              <Route path="/terms-of-service" element={<TermsOfServicePage />} />
              <Route path="/refund-policy" element={<RefundPolicyPage />} />
              <Route path="/blog" element={<BlogPage />} />
              <Route path="/blog/:slug" element={<HybridSEOBlogPostPage />} />
              <Route path="/editorial-blog-demo" element={<EditorialBlogDemo />} />

              {/* Checkout & Payment */}
              <Route path="/checkout" element={<CheckoutPageRobustFixed />} />
              <Route path="/checkout-fixed" element={<CheckoutPageFixed />} />
              <Route path="/checkout-nuclear" element={<CheckoutPageNuclear />} />
              <Route path="/checkout-demo" element={<CheckoutDemo />} />
              <Route path="/checkout-demo-oneway" element={<CheckoutDemoOneWay />} />
              <Route path="/checkout-demo-return" element={<CheckoutDemoReturn />} />
              <Route path="/checkout-test" element={<CheckoutTestPage />} />
              <Route path="/flight-ticket-demo" element={<FlightTicketDemo />} />
              <Route path="/success" element={<SuccessPage />} />

              {/* Debug/Test Routes (only in development) */}
              {process.env.NODE_ENV === 'development' && (
                <>
                  <Route path="/debug" element={<DebugPage />} />
                  <Route path="/checkout-debug" element={<CheckoutDebug />} />
                  <Route path="/checkout-debug-page" element={<CheckoutDebugPage />} />
                  <Route path="/checkout-debug-test" element={<CheckoutDebugTest />} />
                  <Route path="/checkout-simple-fixed" element={<CheckoutSimpleFixed />} />
                  <Route path="/checkout-flow-test" element={<CheckoutFlowTest />} />
                  <Route path="/api-test" element={<APITestPage />} />
                  <Route path="/passenger-details-demo" element={<PassengerDetailsDemo />} />
                  <Route path="/checkout-pricing-test" element={<CheckoutPricingTest />} />
                  <Route path="/seo-test" element={<SEOTestPage />} />

                </>
              )}

              {/* Catch-all redirect to home */}
              <Route path="*" element={<HomePage />} />
            </Routes>
          </main>

          <Footer />

          {/* Toast notifications */}
          <ToastContainer
            position="top-right"
            autoClose={3000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
        </div>
      </Router>
    </BookingProvider>
  );
}
export default App;
