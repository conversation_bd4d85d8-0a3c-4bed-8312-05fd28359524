import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getBlogPostBySlug, getAllBlogPosts } from '../data/blogPosts';
import HybridSEOBlogTemplate from '../components/HybridSEOBlogTemplate';
import { trackBlogView } from '../utils/tracking';
import { 
  generateAllStructuredData, 
  calculateSEOScore,
  generateMetaTags,
  generateCanonicalURL
} from '../utils/hybridSEO';

const HybridSEOBlogPostPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [seoScore, setSeoScore] = useState(null);

  useEffect(() => {
    const fetchPost = async () => {
      try {
        const blogPost = getBlogPostBySlug(slug);
        
        if (!blogPost) {
          navigate('/blog', { replace: true });
          return;
        }

        setPost(blogPost);
        
        // Track blog view
        trackBlogView(blogPost.id, blogPost.title);
        
        // Calculate SEO score for development/admin purposes
        const score = calculateSEOScore(blogPost);
        setSeoScore(score);
        
        // Log SEO score in development
        if (process.env.NODE_ENV === 'development') {
          console.log('📊 SEO Score:', score);
        }
        
      } catch (error) {
        console.error('Error fetching blog post:', error);
        navigate('/blog', { replace: true });
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [slug, navigate]);

  // Update document head with SEO meta tags
  useEffect(() => {
    if (!post) return;

    // Set document title
    document.title = post.metaTitle || `${post.title} | VerifiedOnward`;
    
    // Generate and apply meta tags
    const metaTags = generateMetaTags(post);
    
    // Remove existing meta tags
    const existingMetas = document.querySelectorAll('meta[data-hybrid-seo]');
    existingMetas.forEach(meta => meta.remove());
    
    // Add new meta tags
    metaTags.forEach(tag => {
      const meta = document.createElement('meta');
      
      if (tag.name) {
        meta.setAttribute('name', tag.name);
      } else if (tag.property) {
        meta.setAttribute('property', tag.property);
      }
      
      meta.setAttribute('content', tag.content);
      meta.setAttribute('data-hybrid-seo', 'true');
      document.head.appendChild(meta);
    });

    // Add canonical link
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', generateCanonicalURL(post.slug));

    // Generate and inject structured data
    const schemas = generateAllStructuredData(post);
    
    // Remove existing structured data
    const existingSchemas = document.querySelectorAll('script[data-hybrid-seo-schema]');
    existingSchemas.forEach(script => script.remove());
    
    // Add new structured data
    schemas.forEach((schema, index) => {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.textContent = JSON.stringify(schema, null, 2);
      script.setAttribute('data-hybrid-seo-schema', `schema-${index}`);
      document.head.appendChild(script);
    });

    // Cleanup function
    return () => {
      // Remove meta tags on unmount
      const metas = document.querySelectorAll('meta[data-hybrid-seo]');
      metas.forEach(meta => meta.remove());
      
      // Remove structured data on unmount
      const schemas = document.querySelectorAll('script[data-hybrid-seo-schema]');
      schemas.forEach(script => script.remove());
    };
  }, [post]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center"
        >
          <div className="w-12 h-12 border-4 border-brand-200 border-t-brand-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading article...</p>
        </motion.div>
      </div>
    );
  }

  // Post not found
  if (!post) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md mx-auto px-4"
        >
          <div className="text-6xl mb-4">📄</div>
          <h1 className="text-2xl font-bold text-neutral-900 mb-4">
            Article Not Found
          </h1>
          <p className="text-neutral-600 mb-8">
            The article you're looking for doesn't exist or has been moved.
          </p>
          <button
            onClick={() => navigate('/blog')}
            className="bg-brand-600 hover:bg-brand-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors duration-300"
          >
            Back to Blog
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Score Display (Development Only) */}
      {process.env.NODE_ENV === 'development' && seoScore && (
        <div className="fixed top-4 right-4 z-50 bg-white rounded-lg shadow-lg p-4 border border-neutral-200 max-w-xs">
          <div className="text-sm font-semibold mb-2">SEO Score</div>
          <div className="flex items-center mb-2">
            <div className={`text-2xl font-bold mr-2 ${
              seoScore.grade === 'A' ? 'text-green-600' :
              seoScore.grade === 'B' ? 'text-blue-600' :
              seoScore.grade === 'C' ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {seoScore.grade}
            </div>
            <div className="text-sm text-neutral-600">
              {seoScore.score}/{seoScore.maxScore} ({seoScore.percentage}%)
            </div>
          </div>
          <div className="space-y-1">
            {seoScore.checks.slice(0, 3).map((check, index) => (
              <div key={index} className="flex items-center text-xs">
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  check.status === 'pass' ? 'bg-green-500' :
                  check.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span className="truncate">{check.name}</span>
              </div>
            ))}
            {seoScore.checks.length > 3 && (
              <div className="text-xs text-neutral-500">
                +{seoScore.checks.length - 3} more checks
              </div>
            )}
          </div>
        </div>
      )}

      {/* Main Blog Content */}
      <HybridSEOBlogTemplate post={post} />

      {/* Related Articles Section */}
      <RelatedArticles currentPost={post} />
    </>
  );
};

// Related Articles Component
const RelatedArticles = ({ currentPost }) => {
  const navigate = useNavigate();
  const allPosts = getAllBlogPosts();
  
  // Get related posts based on tags
  const relatedPosts = allPosts
    .filter(post => 
      post.id !== currentPost.id && 
      post.tags.some(tag => currentPost.tags.includes(tag))
    )
    .slice(0, 3);

  if (relatedPosts.length === 0) return null;

  return (
    <section className="py-16 bg-neutral-50">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-6xl mx-auto"
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Related Articles
            </h2>
            <p className="text-lg text-neutral-600">
              Continue reading our expert travel guides
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {relatedPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-soft border border-neutral-100 overflow-hidden hover:shadow-luxury hover:-translate-y-2 transition-all duration-300 cursor-pointer"
                onClick={() => navigate(`/blog/${post.slug}`)}
              >
                <div className="p-6">
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 2).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-3 py-1 text-xs font-medium bg-brand-100 text-brand-700 rounded-full border border-brand-200/50"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  <h3 className="text-xl font-bold text-neutral-900 mb-3 line-clamp-2 hover:text-brand-600 transition-colors">
                    {post.title}
                  </h3>
                  
                  <p className="text-neutral-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-neutral-500">
                    <span>{post.publishDate}</span>
                    <span>{post.readTime}</span>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HybridSEOBlogPostPage;
