import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const CheckoutPageBookingTest = () => {
  console.log('🚨 CheckoutPageBookingTest: Component starting...');
  
  const [step, setStep] = useState(1);
  const [error, setError] = useState(null);
  
  // Test basic hooks
  const navigate = useNavigate();
  const location = useLocation();
  console.log('✅ Step 1: Basic hooks loaded');
  
  // Test BookingContext
  let bookingData = null;
  try {
    console.log('🔍 Step 2: Testing useBooking hook...');
    const booking = useBooking();
    bookingData = booking;
    console.log('✅ Step 2: useBooking successful:', booking);
    setStep(2);
  } catch (err) {
    console.error('❌ Step 2: useBooking failed:', err);
    setError(`BookingContext error: ${err.message}`);
  }
  
  // Test useEffect
  useEffect(() => {
    if (step >= 2) {
      console.log('✅ Step 3: useEffect running with BookingContext');
      setStep(3);
    }
  }, [step]);
  
  if (error) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-lg">
          <h1 className="text-xl font-bold text-red-600 mb-4">BookingContext Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <div className="bg-gray-100 p-4 rounded text-sm">
            <p className="font-medium">Debug Info:</p>
            <p>Component: CheckoutPageBookingTest</p>
            <p>Step reached: {step}</p>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-purple-500 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Checkout BookingContext Test
        </h1>
        
        <div className="space-y-4">
          <div className={`p-3 rounded ${step >= 1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
            ✅ Step 1: Basic hooks loaded
          </div>
          
          <div className={`p-3 rounded ${step >= 2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
            {step >= 2 ? '✅' : '⏳'} Step 2: useBooking successful
          </div>
          
          <div className={`p-3 rounded ${step >= 3 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
            {step >= 3 ? '✅' : '⏳'} Step 3: useEffect with BookingContext
          </div>
        </div>
        
        {step >= 3 && bookingData && (
          <div className="mt-6 p-4 bg-blue-100 rounded">
            <p className="text-blue-800 font-medium">🎉 BookingContext working!</p>
            <div className="text-xs text-blue-600 mt-2">
              <p>Selected Flight: {bookingData.selectedFlight ? 'Present' : 'None'}</p>
              <p>Passengers: {bookingData.passengers?.length || 0}</p>
              <p>Email: {bookingData.email || 'None'}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CheckoutPageBookingTest;
