import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const CheckoutDebugTest = () => {
  const [bookingData, setBookingData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();

  // Create test mode data
  const createTestModeData = () => {
    const urlParams = new URLSearchParams(location.search);
    const tripType = urlParams.get('tripType') || 'oneWay';

    return {
      selectedFlight: {
        id: 'test-flight-123',
        airline: 'Emirates',
        flightNumber: 'EK205',
        departure: {
          airport: 'JFK',
          city: 'New York',
          time: '10:00',
          date: '2025-02-15'
        },
        arrival: {
          airport: 'LHR',
          city: 'London',
          time: '22:00',
          date: '2025-02-15'
        },
        price: tripType === 'return' ? 9.98 : 4.99,
        duration: '8h 00m'
      },
      passengers: [
        { id: 1, firstName: 'John', lastName: 'Smith' }
      ],
      email: '<EMAIL>',
      tripType: tripType
    };
  };

  useEffect(() => {
    console.log('🔍 DEBUG TEST: Initializing...');
    console.log('🔍 DEBUG TEST: Location search:', location.search);

    const urlParams = new URLSearchParams(location.search);
    const isTestMode = urlParams.get('test') === 'true';
    console.log('🔍 DEBUG TEST: isTestMode:', isTestMode);

    if (isTestMode) {
      const testData = createTestModeData();
      console.log('🔍 DEBUG TEST: Created test data:', testData);
      console.log('🔍 DEBUG TEST: selectedFlight:', testData.selectedFlight);
      setBookingData(testData);
    }

    setIsLoading(false);
  }, [location]);

  useEffect(() => {
    console.log('🔍 DEBUG TEST: BookingData changed:', bookingData);
    console.log('🔍 DEBUG TEST: selectedFlight exists:', !!bookingData?.selectedFlight);
  }, [bookingData]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Checkout Debug Test</h1>

      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h2>Raw Data</h2>
        <pre>{JSON.stringify(bookingData, null, 2)}</pre>
      </div>

      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h2>Flight Details Test</h2>
        {bookingData?.selectedFlight ? (
          <div>
            <p><strong>Airline:</strong> {bookingData.selectedFlight.airline}</p>
            <p><strong>Flight:</strong> {bookingData.selectedFlight.flightNumber}</p>
            <p><strong>Route:</strong> {bookingData.selectedFlight.departure?.airport} → {bookingData.selectedFlight.arrival?.airport}</p>
            <p><strong>Date:</strong> {bookingData.selectedFlight.departure?.date}</p>
            <p><strong>Time:</strong> {bookingData.selectedFlight.departure?.time} - {bookingData.selectedFlight.arrival?.time}</p>
          </div>
        ) : (
          <p style={{ color: 'red' }}>No flight selected</p>
        )}
      </div>

      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h2>Conditional Check Results</h2>
        <p>bookingData exists: {bookingData ? 'YES' : 'NO'}</p>
        <p>selectedFlight exists: {bookingData?.selectedFlight ? 'YES' : 'NO'}</p>
        <p>airline exists: {bookingData?.selectedFlight?.airline ? 'YES' : 'NO'}</p>
        <p>departure exists: {bookingData?.selectedFlight?.departure ? 'YES' : 'NO'}</p>
        <p>arrival exists: {bookingData?.selectedFlight?.arrival ? 'YES' : 'NO'}</p>
      </div>
    </div>
  );
};

export default CheckoutDebugTest;
