import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useBooking } from '../context/BookingContext';
import StripePayment from '../components/StripePayment';
import PayPalPayment from '../components/PayPalPayment';
import { ticketAPI } from '../services/api';
import { generateReservationCode } from '../utils/reservationCodeGenerator';
import {
  LockClosedIcon,
  ShieldCheckIcon,
  CreditCardIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  UserIcon,
  EnvelopeIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';

const CheckoutPageFixed = () => {
  console.log('🔍 CheckoutPageFixed: Component rendering...');

  const navigate = useNavigate();
  const [paymentMethod, setPaymentMethod] = useState('stripe');
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [contextError, setContextError] = useState(null);

  // Safely get booking data with comprehensive error handling
  let bookingData = null;
  try {
    console.log('🔍 CheckoutPageFixed: Attempting to access booking context...');
    bookingData = useBooking();
    console.log('🔍 CheckoutPageFixed: Booking context loaded successfully:', bookingData);

    // Validate that we have the essential booking context structure
    if (!bookingData || typeof bookingData !== 'object') {
      throw new Error('BookingContext returned invalid data structure');
    }

    // Check if context is properly initialized
    if (bookingData.isLoaded === false) {
      console.log('🔍 CheckoutPageFixed: Context not yet loaded, waiting...');
    }

  } catch (err) {
    console.error('🚨 CheckoutPageFixed: Error accessing booking context:', err);
    console.error('🚨 CheckoutPageFixed: Error stack:', err.stack);
    setContextError(`BookingContext Error: ${err.message}`);

    // Set comprehensive fallback data to prevent blank page
    bookingData = {
      selectedFlight: null,
      selectedOutboundFlight: null,
      selectedReturnFlight: null,
      passengers: [{ id: 1, firstName: '', lastName: '' }],
      email: '',
      tripType: 'oneWay',
      isLoaded: true,
      step: 'checkout'
    };
    console.log('🔧 CheckoutPageFixed: Using fallback booking data:', bookingData);
  }

  // Extract data safely with fallbacks
  const {
    selectedFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers = [],
    email = '',
    tripType = 'oneWay'
  } = bookingData || {};

  // Use the correct flight data structure
  const departureFlightData = selectedFlight || selectedOutboundFlight;
  const returnFlightData = selectedReturnFlight;

  // Check for bypass mode
  const urlParams = new URLSearchParams(window.location.search);
  const bypassValidation = urlParams.get('bypass') === 'true';

  // Create fallback data for testing/demo
  const createFallbackData = () => ({
    departureFlightData: {
      id: 'demo-flight-1',
      flight: {
        number: 'BA 123',
        departure: {
          airport: 'LHR',
          iataCode: 'LHR',
          city: 'London Heathrow',
          time: '2025-07-15 10:00'
        },
        arrival: {
          airport: 'JFK',
          iataCode: 'JFK',
          city: 'New York JFK',
          time: '2025-07-15 18:00'
        },
        duration: '8h 00m'
      },
      airline: {
        name: 'British Airways',
        code: 'BA',
        logo: 'https://www.gstatic.com/flights/airline_logos/70px/BA.png'
      },
      price: {
        total: 4.99,
        currency: 'USD',
        displayPrice: 4.99,
        originalPrice: 650
      }
    },
    passengers: [{
      id: 1,
      firstName: 'Demo',
      lastName: 'User',
      dateOfBirth: '1990-01-01'
    }],
    email: '<EMAIL>'
  });

  // Determine final data to use
  let finalDepartureFlightData = departureFlightData;
  let finalReturnFlightData = returnFlightData;
  let finalPassengers = passengers;
  let finalEmail = email;

  // Validation
  const hasValidationErrors = !finalDepartureFlightData || !finalPassengers.length || !finalEmail;
  const validationErrors = [];

  if (!finalDepartureFlightData) validationErrors.push('No flight selected');
  if (!finalPassengers.length) validationErrors.push('No passenger information');
  if (!finalEmail) validationErrors.push('No email provided');

  // Use fallback data if bypassing or if there are context errors
  if ((bypassValidation && hasValidationErrors) || contextError) {
    console.log('🔧 CheckoutPageFixed: Using fallback data...');
    const fallbackData = createFallbackData();
    finalDepartureFlightData = fallbackData.departureFlightData;
    finalPassengers = fallbackData.passengers;
    finalEmail = fallbackData.email;
  }

  // Complete loading after data is processed
  useEffect(() => {
    console.log('🔍 CheckoutPageFixed: Processing data and completing load...');

    const timer = setTimeout(() => {
      setIsLoading(false);
      console.log('🔍 CheckoutPageFixed: Loading complete');
    }, 500); // Reduced loading time

    return () => clearTimeout(timer);
  }, [bookingData]);

  // Handle payment success
  const handlePaymentSuccess = async (paymentData) => {
    console.log('🎉 CheckoutPageFixed: Payment successful:', paymentData);
    setIsProcessingPayment(true);

    try {
      // Generate booking reference
      const bookingRef = generateReservationCode();
      
      // Navigate to success page
      navigate('/success', {
        state: {
          bookingReference: bookingRef,
          paymentData,
          selectedFlight: finalDepartureFlightData,
          returnFlight: returnFlightData,
          passengers: finalPassengers,
          email: finalEmail,
          tripType
        }
      });
    } catch (err) {
      console.error('🚨 CheckoutPageFixed: Error processing payment:', err);
      setError('Payment processing failed. Please try again.');
      setIsProcessingPayment(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout...</p>
        </div>
      </div>
    );
  }

  // Show validation errors (unless bypassed or context error)
  if (hasValidationErrors && !bypassValidation && !contextError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Missing Information
          </h2>
          <div className="text-left mb-6">
            <p className="text-gray-600 mb-2">Please complete the following:</p>
            <ul className="list-disc list-inside text-red-600 space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/search')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Over
            </button>
            <button
              onClick={() => window.location.href = '/checkout?bypass=true'}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-sm"
            >
              Continue Anyway (Debug)
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show context error with fallback option
  if (contextError && !bypassValidation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="text-orange-500 text-6xl mb-4">🔧</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            System Error
          </h2>
          <div className="text-left mb-6">
            <p className="text-gray-600 mb-2">There was an issue loading your booking data:</p>
            <p className="text-red-600 text-sm bg-red-50 p-2 rounded">{contextError}</p>
          </div>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/search')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Over
            </button>
            <button
              onClick={() => window.location.href = '/checkout?bypass=true'}
              className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors text-sm"
            >
              Continue with Demo Data
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Complete Your Booking
          </h1>
          <p className="text-gray-600">
            Secure checkout for your dummy flight ticket
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Flight Summary */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-lg p-6 mb-6"
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <PaperAirplaneIcon className="h-5 w-5 mr-2 text-blue-600" />
                Flight Summary
              </h2>
              
              {/* Departure Flight */}
              <div className="border rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    {finalDepartureFlightData?.airline?.logo ? (
                      <img
                        src={finalDepartureFlightData.airline.logo}
                        alt={finalDepartureFlightData.airline.name}
                        className="h-8 w-8 mr-3"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="h-8 w-8 mr-3 bg-blue-100 rounded flex items-center justify-center">
                        <PaperAirplaneIcon className="h-4 w-4 text-blue-600" />
                      </div>
                    )}
                    <div>
                      <p className="font-semibold">{finalDepartureFlightData?.airline?.name || 'Airline'}</p>
                      <p className="text-sm text-gray-600">{finalDepartureFlightData?.flight?.number || 'Flight Number'}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      ${finalDepartureFlightData?.price?.displayPrice || finalDepartureFlightData?.price?.total || '4.99'}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>
                    {finalDepartureFlightData?.flight?.departure?.city || 'Departure'}
                    ({finalDepartureFlightData?.flight?.departure?.iataCode || 'DEP'})
                  </span>
                  <span>→</span>
                  <span>
                    {finalDepartureFlightData?.flight?.arrival?.city || 'Arrival'}
                    ({finalDepartureFlightData?.flight?.arrival?.iataCode || 'ARR'})
                  </span>
                </div>
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>{finalDepartureFlightData?.flight?.departure?.time || 'Departure Time'}</span>
                  <span>{finalDepartureFlightData?.flight?.arrival?.time || 'Arrival Time'}</span>
                </div>
              </div>

              {/* Return Flight (if applicable) */}
              {finalReturnFlightData && (
                <div className="border rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {finalReturnFlightData?.airline?.logo ? (
                        <img
                          src={finalReturnFlightData.airline.logo}
                          alt={finalReturnFlightData.airline.name}
                          className="h-8 w-8 mr-3"
                          onError={(e) => {
                            e.target.style.display = 'none';
                          }}
                        />
                      ) : (
                        <div className="h-8 w-8 mr-3 bg-blue-100 rounded flex items-center justify-center">
                          <PaperAirplaneIcon className="h-4 w-4 text-blue-600" />
                        </div>
                      )}
                      <div>
                        <p className="font-semibold">{finalReturnFlightData?.airline?.name || 'Airline'}</p>
                        <p className="text-sm text-gray-600">{finalReturnFlightData?.flight?.number || 'Flight Number'}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">
                        ${finalReturnFlightData?.price?.displayPrice || finalReturnFlightData?.price?.total || '4.99'}
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>
                      {finalReturnFlightData?.flight?.departure?.city || 'Departure'}
                      ({finalReturnFlightData?.flight?.departure?.iataCode || 'DEP'})
                    </span>
                    <span>→</span>
                    <span>
                      {finalReturnFlightData?.flight?.arrival?.city || 'Arrival'}
                      ({finalReturnFlightData?.flight?.arrival?.iataCode || 'ARR'})
                    </span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500 mt-1">
                    <span>{finalReturnFlightData?.flight?.departure?.time || 'Departure Time'}</span>
                    <span>{finalReturnFlightData?.flight?.arrival?.time || 'Arrival Time'}</span>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Passenger Details */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-lg shadow-lg p-6 mb-6"
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <UserIcon className="h-5 w-5 mr-2 text-blue-600" />
                Passenger Details
              </h2>
              
              {finalPassengers.map((passenger, index) => (
                <div key={index} className="border rounded-lg p-4 mb-3 last:mb-0">
                  <p className="font-semibold">Passenger {index + 1}</p>
                  <p className="text-gray-600">{passenger.firstName} {passenger.lastName}</p>
                  <p className="text-sm text-gray-500">DOB: {passenger.dateOfBirth}</p>
                </div>
              ))}

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800 flex items-center">
                  <EnvelopeIcon className="h-4 w-4 mr-2" />
                  Ticket will be sent to: {finalEmail}
                </p>
              </div>
            </motion.div>
          </div>

          {/* Payment Section */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg shadow-lg p-6 sticky top-8"
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <CreditCardIcon className="h-5 w-5 mr-2 text-blue-600" />
                Payment
              </h2>

              {/* Total */}
              <div className="border-b pb-4 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">Total</span>
                  <span className="price-checkout">
                    ${((finalDepartureFlightData?.price?.displayPrice || finalDepartureFlightData?.price?.total || 4.99) +
                       (finalReturnFlightData?.price?.displayPrice || finalReturnFlightData?.price?.total || 0)).toFixed(2)}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {finalPassengers.length} passenger{finalPassengers.length > 1 ? 's' : ''}
                </p>
              </div>

              {/* Payment Method Selection */}
              <div className="mb-6">
                <div className="flex space-x-2 mb-4">
                  <button
                    onClick={() => setPaymentMethod('stripe')}
                    className={`flex-1 py-2 px-3 rounded-lg border text-sm font-medium transition-colors ${
                      paymentMethod === 'stripe'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    💳 Card
                  </button>
                  <button
                    onClick={() => setPaymentMethod('paypal')}
                    className={`flex-1 py-2 px-3 rounded-lg border text-sm font-medium transition-colors ${
                      paymentMethod === 'paypal'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    🅿️ PayPal
                  </button>
                </div>

                {/* Payment Component */}
                {paymentMethod === 'stripe' && (
                  <StripePayment
                    amount={(finalDepartureFlightData?.price?.displayPrice || finalDepartureFlightData?.price?.total || 4.99) +
                           (finalReturnFlightData?.price?.displayPrice || finalReturnFlightData?.price?.total || 0)}
                    onSuccess={handlePaymentSuccess}
                    onError={(err) => setError(err.message)}
                    disabled={isProcessingPayment}
                  />
                )}

                {paymentMethod === 'paypal' && (
                  <PayPalPayment
                    amount={(finalDepartureFlightData?.price?.displayPrice || finalDepartureFlightData?.price?.total || 4.99) +
                           (finalReturnFlightData?.price?.displayPrice || finalReturnFlightData?.price?.total || 0)}
                    onSuccess={handlePaymentSuccess}
                    onError={(err) => setError(err.message)}
                    disabled={isProcessingPayment}
                  />
                )}
              </div>

              {/* Security Notice */}
              <div className="bg-green-50 rounded-lg p-3 mb-4">
                <div className="flex items-center text-green-800 text-sm">
                  <ShieldCheckIcon className="h-4 w-4 mr-2" />
                  <span>Secure SSL encrypted payment</span>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}

              {/* Back Button */}
              <button
                onClick={() => navigate(-1)}
                className="w-full flex items-center justify-center py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isProcessingPayment}
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Flight Selection
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Error Boundary Wrapper Component
class CheckoutErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('🚨 CheckoutPageFixed Error Boundary caught an error:', error, errorInfo);
    console.error('🚨 Error details:', {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    });

    // Try to save current booking data before crash
    try {
      const currentBookingData = {
        timestamp: new Date().toISOString(),
        error: error.message,
        url: window.location.href,
        userAgent: navigator.userAgent
      };
      localStorage.setItem('checkoutCrashData', JSON.stringify(currentBookingData));
      console.log('💾 Crash data saved to localStorage');
    } catch (saveError) {
      console.error('Failed to save crash data:', saveError);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
            <div className="text-red-500 text-6xl mb-4">💥</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h2>
            <div className="text-left mb-6">
              <p className="text-gray-600 mb-2">The checkout page encountered an error:</p>
              <p className="text-red-600 text-sm bg-red-50 p-2 rounded mb-3">
                {this.state.error?.message || 'Unknown error occurred'}
              </p>
              <details className="text-xs text-gray-500">
                <summary className="cursor-pointer hover:text-gray-700">Technical Details</summary>
                <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono">
                  <p><strong>Error:</strong> {this.state.error?.name}</p>
                  <p><strong>Message:</strong> {this.state.error?.message}</p>
                  <p><strong>Time:</strong> {new Date().toLocaleString()}</p>
                  <p><strong>URL:</strong> {window.location.href}</p>
                </div>
              </details>
            </div>
            <div className="space-y-3">
              <button
                onClick={() => window.location.href = '/search'}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start Over
              </button>
              <button
                onClick={() => window.location.href = '/checkout?bypass=true'}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                Try Demo Mode
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Wrapped component with error boundary
const CheckoutPageWithErrorBoundary = () => {
  return (
    <CheckoutErrorBoundary>
      <CheckoutPageFixed />
    </CheckoutErrorBoundary>
  );
};

export default CheckoutPageWithErrorBoundary;
