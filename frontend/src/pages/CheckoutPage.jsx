import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ticketAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import StripePayment from '../components/StripePayment';
import PayPalPayment from '../components/PayPalPayment';
import ErrorBoundary from '../components/ErrorBoundary';

// Flight Card Component
const FlightCard = ({ flight }) => {
  const formatTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 font-semibold text-sm">
              {flight.airline?.code || flight.airline?.substring(0, 2) || 'FL'}
            </span>
          </div>
          <div>
            <p className="font-semibold text-gray-900">
              {flight.airline?.name || flight.airline || 'Airline'}
            </p>
            <p className="text-sm text-gray-500">
              Flight {flight.flightNumber || flight.flight?.number || 'N/A'}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="flex items-center justify-end gap-2 mb-2">
            {flight.price?.originalPrice && (
              <span className="price-previous text-sm">
                ${flight.price.originalPrice}
              </span>
            )}
            <div className="price-checkout">
              $4.99
            </div>
          </div>
          <p className="text-xs text-gray-500">per person</p>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900">
            {flight.departure?.iataCode || flight.departure?.airport || 'DEP'}
          </p>
          <p className="text-sm text-gray-500">
            {formatTime(flight.departure?.time)}
          </p>
          <p className="text-xs text-gray-400">
            {formatDate(flight.departure?.time)}
          </p>
        </div>

        <div className="flex-1 mx-4">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center">
              <span className="bg-white px-3 text-gray-500">
                ✈️ {flight.duration || '2h 30m'}
              </span>
            </div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900">
            {flight.arrival?.iataCode || flight.arrival?.airport || 'ARR'}
          </p>
          <p className="text-sm text-gray-500">
            {formatTime(flight.arrival?.time)}
          </p>
          <p className="text-xs text-gray-400">
            {formatDate(flight.arrival?.time)}
          </p>
        </div>
      </div>
    </div>
  );
};

const CheckoutPage = () => {
  console.log('🔍 CheckoutPage: Component rendering...');
  const navigate = useNavigate();
  const location = useLocation();

  // Get data from navigation state or localStorage backup
  const [checkoutData, setCheckoutData] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('stripe');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log('🔍 CheckoutPage: Loading checkout data...');
    console.log('🔍 CheckoutPage: Location state:', location.state);

    let data = null;

    // First try to get data from navigation state
    if (location.state) {
      console.log('✅ CheckoutPage: Using data from navigation state');
      data = location.state;
    } else {
      // Fallback to localStorage
      console.log('⚠️ CheckoutPage: No navigation state, trying localStorage...');
      try {
        const nuclearData = localStorage.getItem('nuclearCheckoutData');
        const regularData = localStorage.getItem('checkoutBackupData');

        if (nuclearData) {
          console.log('✅ CheckoutPage: Using nuclear checkout data from localStorage');
          data = JSON.parse(nuclearData);
        } else if (regularData) {
          console.log('✅ CheckoutPage: Using regular checkout data from localStorage');
          data = JSON.parse(regularData);
        }
      } catch (error) {
        console.error('❌ CheckoutPage: Error parsing localStorage data:', error);
      }
    }

    if (data) {
      console.log('✅ CheckoutPage: Checkout data loaded:', data);
      setCheckoutData(data);
    } else {
      console.log('❌ CheckoutPage: No checkout data available');
    }

    setIsLoading(false);
  }, [location.state]);

  // Extract data for easier access
  const passengers = checkoutData?.passengers || [];
  const email = checkoutData?.email || '';
  const tripType = checkoutData?.tripType || 'oneWay';
  const totalPrice = checkoutData?.totalPrice || 4.99;

  // Validation check
  const hasValidData = checkoutData && passengers.length > 0 && email;

  console.log('🔍 CheckoutPage: Data validation:', {
    hasCheckoutData: !!checkoutData,
    hasPassengers: passengers.length > 0,
    hasEmail: !!email,
    tripType,
    totalPrice
  });

  const handlePaymentSuccess = async (paymentResult) => {
    setIsProcessing(true);
    setError(null);

    try {
      console.log('✅ CheckoutPage: Payment successful:', paymentResult);

      // Prepare flight data based on trip type
      let flightData;
      if (tripType === 'return') {
        flightData = {
          tripType: 'return',
          outboundFlight: checkoutData.outboundFlight,
          returnFlight: checkoutData.returnFlight,
          searchData: checkoutData.searchData
        };
      } else {
        flightData = checkoutData.flight;
      }

      const ticketData = {
        flightData: flightData,
        passengerData: passengers,
        paymentId: paymentResult.paymentId,
        email: email,
        tripType: tripType
      };

      console.log('🎫 CheckoutPage: Generating ticket with data:', ticketData);

      try {
        const ticketResponse = await ticketAPI.generateTicket(ticketData);
        console.log('✅ CheckoutPage: Ticket generated successfully:', ticketResponse);

        // Navigate to success page with payment result
        navigate('/success', {
          state: {
            paymentId: paymentResult.paymentId,
            bookingReference: ticketResponse.bookingReference,
            email: email,
            passengers: passengers,
            flightData: flightData,
            totalPrice: totalPrice
          }
        });
      } catch (ticketError) {
        console.error('❌ CheckoutPage: Ticket generation error:', ticketError);
        // Even if ticket generation fails, we can still show success
        // since payment was successful
        navigate('/success', {
          state: {
            paymentId: paymentResult.paymentId,
            email: email,
            passengers: passengers,
            flightData: flightData,
            totalPrice: totalPrice,
            ticketError: true
          }
        });
      }
    } catch (error) {
      console.error('❌ CheckoutPage: Post-payment processing error:', error);
      // Still navigate to success since payment was successful
      navigate('/success', {
        state: {
          paymentId: paymentResult.paymentId,
          email: email,
          passengers: passengers,
          totalPrice: totalPrice,
          error: true
        }
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentError = (errorMessage) => {
    setError(errorMessage);
    setIsProcessing(false);
  };

  // Show loading spinner while data is being loaded
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  // If missing critical data, show error page
  if (!hasValidData) {
    return (
      <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Missing Booking Data</h2>
            <p className="text-gray-600 mb-6">
              We couldn't find your booking information. Please return to the flight selection page and try again.
            </p>

            <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold mb-3 text-center">Current Status:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div className="flex items-center">
                  <span className={passengers.length > 0 ? 'text-green-600' : 'text-red-600'}>
                    {passengers.length > 0 ? '✅' : '❌'}
                  </span>
                  <span className="ml-2">
                    Passengers: {passengers.length > 0 ? `${passengers.length} added` : 'Missing'}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className={email ? 'text-green-600' : 'text-red-600'}>
                    {email ? '✅' : '❌'}
                  </span>
                  <span className="ml-2">
                    Email: {email ? 'Provided' : 'Missing'}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className={checkoutData ? 'text-green-600' : 'text-red-600'}>
                    {checkoutData ? '✅' : '❌'}
                  </span>
                  <span className="ml-2">
                    Checkout Data: {checkoutData ? 'Present' : 'Missing'}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className={checkoutData?.flight || checkoutData?.outboundFlight ? 'text-green-600' : 'text-red-600'}>
                    {checkoutData?.flight || checkoutData?.outboundFlight ? '✅' : '❌'}
                  </span>
                  <span className="ml-2">
                    Flight: {checkoutData?.flight || checkoutData?.outboundFlight ? 'Selected' : 'Missing'}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-x-4">
              <button
                onClick={() => navigate('/')}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold"
              >
                Start New Booking
              </button>
              <button
                onClick={() => window.location.reload()}
                className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 font-semibold"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary fallbackMessage="There was an error loading the checkout page. Please try refreshing or start a new booking.">
      <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Review & Payment
            </h1>
            <p className="text-gray-600">
              Review your booking details and complete payment
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Flight Details & Passenger Info */}
            <div className="lg:col-span-2 space-y-6">
            {/* Flight Details */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Flight Details
              </h3>

              {/* Render flight details based on trip type */}
              {tripType === 'oneWay' && checkoutData.flight && (
                <FlightCard flight={checkoutData.flight} />
              )}

              {tripType === 'return' && (
                <div className="space-y-4">
                  {checkoutData.outboundFlight && (
                    <div>
                      <h4 className="text-md font-medium text-gray-700 mb-2">Departure Flight</h4>
                      <FlightCard flight={checkoutData.outboundFlight} />
                    </div>
                  )}
                  {checkoutData.returnFlight && (
                    <div>
                      <h4 className="text-md font-medium text-gray-700 mb-2">Return Flight</h4>
                      <FlightCard flight={checkoutData.returnFlight} />
                    </div>
                  )}
                </div>
              )}
            </motion.div>

            {/* Passenger Information */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Passenger Information
              </h3>
              
              <div className="space-y-3">
                {passengers.map((passenger, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">
                        {passenger.firstName} {passenger.lastName}
                      </p>
                      <p className="text-sm text-gray-500">Passenger {index + 1}</p>
                    </div>
                    <span className="text-green-600 text-sm">✓ Confirmed</span>
                  </div>
                ))}
                
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Email:</strong> {email}
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Your ticket will be sent to this email address
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Payment Method Selection */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Choose Payment Method
              </h3>
              
              <div className="space-y-3 mb-6">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    value="stripe"
                    checked={paymentMethod === 'stripe'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Credit/Debit Card</span>
                    <span className="text-sm text-gray-500">(Stripe)</span>
                  </div>
                </label>
                
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    value="paypal"
                    checked={paymentMethod === 'paypal'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">PayPal</span>
                  </div>
                </label>
              </div>

              {/* Payment Form */}
              {paymentMethod === 'stripe' && (
                <StripePayment
                  amount={totalPrice}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                />
              )}

              {paymentMethod === 'paypal' && (
                <PayPalPayment
                  amount={totalPrice}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                />
              )}

              {error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}
            </motion.div>
          </div>

            {/* Right Column - Order Summary */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-lg shadow-md p-6 sticky top-8"
              >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Order Summary
              </h3>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Flight Route</span>
                  <span className="font-medium">
                    {tripType === 'oneWay' && checkoutData.flight && (
                      `${checkoutData.flight.departure?.iataCode || 'DEP'} → ${checkoutData.flight.arrival?.iataCode || 'ARR'}`
                    )}
                    {tripType === 'return' && checkoutData.outboundFlight && checkoutData.returnFlight && (
                      `${checkoutData.outboundFlight.departure?.iataCode || 'DEP'} ⇄ ${checkoutData.outboundFlight.arrival?.iataCode || 'ARR'}`
                    )}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Trip Type</span>
                  <span className="font-medium capitalize">{tripType === 'oneWay' ? 'One Way' : 'Return'}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Passengers</span>
                  <span className="font-medium">{passengers.length}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Price per person</span>
                  <span className="price-checkout">${(totalPrice / passengers.length).toFixed(2)}</span>
                </div>

                <hr className="my-4" />

                <div className="flex justify-between items-center text-lg font-bold mb-4">
                  <span>Total</span>
                  <div className="price-checkout">
                    USD ${totalPrice.toFixed(2)}
                  </div>
                </div>


              </div>

              <div className="space-y-4">
                <button
                  onClick={() => navigate(-1)}
                  disabled={isProcessing}
                  className="w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-lg font-semibold hover:bg-gray-300 transition-colors disabled:opacity-50"
                >
                  Back to Details
                </button>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default CheckoutPage;
