import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const CheckoutFlowTest = () => {
  const navigate = useNavigate();
  const booking = useBooking();
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (test, status, message) => {
    setTestResults(prev => [...prev, { test, status, message, timestamp: new Date().toLocaleString() }]);
  };

  const simulateFlightSummaryToCheckout = () => {
    console.log('🧪 Simulating Flight Summary to Checkout navigation...');
    addTestResult('Navigation Simulation', 'running', 'Starting simulation...');

    try {
      // Step 1: Set up booking data (like FlightSummaryWithPassengerForm would do)
      const mockFlightData = {
        id: 'test-flight-123',
        airline: 'Test Airlines',
        flightNumber: 'TA123',
        departure: {
          airport: 'JFK',
          city: 'New York',
          time: '10:00',
          date: '2025-02-15'
        },
        arrival: {
          airport: 'LHR',
          city: 'London',
          time: '22:00',
          date: '2025-02-15'
        },
        price: 4.99,
        duration: '8h 00m'
      };

      const mockPassengers = [
        { id: 1, firstName: 'John', lastName: 'Doe' }
      ];

      const mockEmail = '<EMAIL>';

      // Step 2: Update booking context (simulate form submission)
      booking.setSelectedFlight(mockFlightData);
      booking.setPassengers(mockPassengers);
      booking.setEmail(mockEmail);
      booking.setTripType('oneWay');

      addTestResult('Booking Context Update', 'success', 'Mock data set in context');

      // Step 3: Prepare checkout data (like handlePassengerDetailsSubmit would do)
      const checkoutData = {
        selectedFlight: mockFlightData,
        passengers: mockPassengers,
        email: mockEmail,
        tripType: 'oneWay',
        searchData: {
          from: 'JFK',
          to: 'LHR',
          departureDate: '2025-02-15',
          tripType: 'oneWay'
        }
      };

      addTestResult('Checkout Data Preparation', 'success', 'Checkout data prepared');

      // Step 4: Navigate to checkout with state (simulate React Router navigation)
      setTimeout(() => {
        console.log('🚀 Navigating to checkout with state:', checkoutData);
        navigate('/checkout', { state: checkoutData });
        addTestResult('Navigation to Checkout', 'success', 'Navigated with state data');
      }, 1000);

    } catch (error) {
      console.error('❌ Simulation failed:', error);
      addTestResult('Navigation Simulation', 'error', error.message);
    }
  };

  const testDirectCheckoutAccess = () => {
    console.log('🧪 Testing direct checkout access...');
    addTestResult('Direct Access Test', 'running', 'Testing direct checkout access...');

    try {
      navigate('/checkout');
      addTestResult('Direct Access Test', 'success', 'Direct navigation successful');
    } catch (error) {
      addTestResult('Direct Access Test', 'error', error.message);
    }
  };

  const testCheckoutWithTestMode = () => {
    console.log('🧪 Testing checkout with test mode...');
    addTestResult('Test Mode', 'running', 'Testing checkout with ?test=true...');

    try {
      navigate('/checkout?test=true');
      addTestResult('Test Mode', 'success', 'Test mode navigation successful');
    } catch (error) {
      addTestResult('Test Mode', 'error', error.message);
    }
  };

  const clearBookingData = () => {
    console.log('🧹 Clearing booking data...');
    try {
      booking.resetBooking();
      localStorage.clear();
      addTestResult('Data Clear', 'success', 'All booking data cleared');
    } catch (error) {
      addTestResult('Data Clear', 'error', error.message);
    }
  };

  const checkBookingContextStatus = () => {
    console.log('🔍 Checking booking context status...');
    try {
      const status = {
        isLoaded: booking.isLoaded,
        hasSelectedFlight: !!booking.selectedFlight,
        hasPassengers: booking.passengers?.length > 0,
        hasEmail: !!booking.email,
        tripType: booking.tripType,
        step: booking.step
      };
      
      addTestResult('Context Status', 'info', JSON.stringify(status, null, 2));
      console.log('📊 Booking context status:', status);
    } catch (error) {
      addTestResult('Context Status', 'error', error.message);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            🧪 Checkout Flow Test
          </h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800">Test Actions</h2>
              
              <button
                onClick={simulateFlightSummaryToCheckout}
                className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                🚀 Simulate Flight Summary → Checkout
              </button>
              
              <button
                onClick={testDirectCheckoutAccess}
                className="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors"
              >
                🎯 Test Direct Checkout Access
              </button>
              
              <button
                onClick={testCheckoutWithTestMode}
                className="w-full bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors"
              >
                🧪 Test Checkout (Test Mode)
              </button>
              
              <button
                onClick={checkBookingContextStatus}
                className="w-full bg-yellow-600 text-white px-4 py-3 rounded-lg hover:bg-yellow-700 transition-colors"
              >
                🔍 Check Context Status
              </button>
              
              <button
                onClick={clearBookingData}
                className="w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 transition-colors"
              >
                🧹 Clear All Data
              </button>
            </div>

            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800">Current Context</h2>
              <div className="bg-gray-50 rounded-lg p-4 text-sm">
                <div className="space-y-2">
                  <p><strong>Loaded:</strong> {booking.isLoaded ? 'Yes' : 'No'}</p>
                  <p><strong>Selected Flight:</strong> {booking.selectedFlight ? 'Yes' : 'No'}</p>
                  <p><strong>Passengers:</strong> {booking.passengers?.length || 0}</p>
                  <p><strong>Email:</strong> {booking.email || 'None'}</p>
                  <p><strong>Trip Type:</strong> {booking.tripType || 'None'}</p>
                  <p><strong>Step:</strong> {booking.step || 'None'}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Results</h2>
            
            {testResults.length === 0 ? (
              <p className="text-gray-500 italic">No tests run yet. Click a test button above.</p>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border-l-4 ${
                      result.status === 'success' ? 'bg-green-50 border-green-400' :
                      result.status === 'error' ? 'bg-red-50 border-red-400' :
                      result.status === 'running' ? 'bg-blue-50 border-blue-400' :
                      'bg-yellow-50 border-yellow-400'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-gray-900">{result.test}</p>
                        <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                      </div>
                      <div className="text-xs text-gray-500">{result.timestamp}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mt-8 text-center">
            <button
              onClick={() => navigate('/')}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              ← Back to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutFlowTest;
