import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getBlogPostBySlug } from '../data/blogPosts';
import EnhancedBlogRenderer from '../components/EnhancedBlogRenderer';



const SimpleBlogPostPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const foundPost = getBlogPostBySlug(slug);

    if (!foundPost) {
      navigate('/blog');
      return;
    }

    // Set page title
    document.title = `${foundPost.title} | VerifiedOnward`;

    // Set meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', foundPost.metaDescription || foundPost.excerpt);
    }

    setPost(foundPost);
    setLoading(false);
  }, [slug, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500 mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading article...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-neutral-800 mb-4">Article Not Found</h1>
          <p className="text-neutral-600 mb-6">The requested blog post could not be found.</p>
          <button 
            onClick={() => navigate('/blog')}
            className="bg-brand-500 text-white px-6 py-3 rounded-lg hover:bg-brand-600 transition-colors"
          >
            Back to Blog
          </button>
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-neutral-200/50">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <nav className="mb-4">
            <button
              onClick={() => navigate('/blog')}
              className="inline-flex items-center text-brand-500 hover:text-brand-600 font-medium transition-colors group"
            >
              <span className="mr-2 group-hover:-translate-x-1 transition-transform">←</span>
              Back to Blog
            </button>
          </nav>

          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 mb-4 leading-tight">
            {post.title}
          </h1>

          <div className="flex items-center text-neutral-600 text-sm space-x-4 mb-6">
            <span className="bg-neutral-100 px-3 py-1 rounded-full">{post.publishDate}</span>
            <span>•</span>
            <span className="bg-brand-50 text-brand-700 px-3 py-1 rounded-full">{post.readTime}</span>
          </div>

          <p className="text-lg md:text-xl text-neutral-700 leading-relaxed max-w-3xl">
            {post.excerpt}
          </p>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-6 py-12">
        <article className="bg-white rounded-2xl shadow-sm border border-neutral-200/50 p-8 md:p-12">
          <EnhancedBlogRenderer post={post} showTakeaways={true} />
        </article>

        {/* CTA Section */}
        <div className="mt-12 bg-gradient-to-r from-brand-500 to-brand-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">Ready to Get Your Flight Reservation?</h3>
          <p className="text-brand-100 mb-6 max-w-2xl mx-auto">
            Get your embassy-approved flight reservation in minutes. No risk, no hassle.
          </p>
          <button
            onClick={() => {
              navigate('/');
              setTimeout(() => {
                const element = document.getElementById('flight-search');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }, 100);
            }}
            className="bg-white text-brand-600 px-8 py-3 rounded-lg font-semibold hover:bg-neutral-50 transition-colors"
          >
            Start Your Reservation
          </button>
        </div>
      </main>
    </div>
  );
};

export default SimpleBlogPostPage;
