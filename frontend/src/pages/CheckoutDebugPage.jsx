import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const CheckoutDebugPage = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    console.log('🔍 CheckoutDebugPage: Starting debug analysis...');
    
    const debug = {
      timestamp: new Date().toISOString(),
      location: {
        pathname: location.pathname,
        search: location.search,
        state: location.state
      },
      localStorage: {},
      bookingContext: null,
      contextError: null
    };

    // Test localStorage
    try {
      const keys = [
        'bookingSearchData',
        'bookingSelectedFlight',
        'bookingSelectedOutboundFlight',
        'bookingSelectedReturnFlight',
        'bookingPassengers',
        'bookingEmail',
        'bookingTripType'
      ];
      
      keys.forEach(key => {
        const value = localStorage.getItem(key);
        debug.localStorage[key] = value ? JSON.parse(value) : null;
      });
    } catch (err) {
      debug.localStorage.error = err.message;
    }

    // Test BookingContext
    try {
      const bookingData = useBooking();
      debug.bookingContext = {
        isLoaded: bookingData.isLoaded,
        selectedFlight: !!bookingData.selectedFlight,
        selectedOutboundFlight: !!bookingData.selectedOutboundFlight,
        selectedReturnFlight: !!bookingData.selectedReturnFlight,
        passengers: bookingData.passengers?.length || 0,
        email: !!bookingData.email,
        tripType: bookingData.tripType,
        step: bookingData.step
      };
    } catch (err) {
      debug.contextError = err.message;
      setError(err.message);
    }

    setDebugInfo(debug);
    console.log('🔍 Debug info collected:', debug);
  }, [location]);

  const simulateCheckoutNavigation = () => {
    console.log('🧪 Simulating checkout navigation...');
    
    const mockData = {
      selectedFlight: {
        id: 'debug-flight-123',
        airline: 'Debug Airlines',
        flightNumber: 'DB123',
        departure: {
          airport: 'JFK',
          time: '10:00',
          date: '2025-02-01'
        },
        arrival: {
          airport: 'LHR',
          time: '22:00',
          date: '2025-02-01'
        },
        price: 4.99
      },
      passengers: [
        { id: 1, firstName: 'Debug', lastName: 'User' }
      ],
      email: '<EMAIL>',
      tripType: 'oneWay'
    };

    // Save to localStorage
    Object.entries(mockData).forEach(([key, value]) => {
      localStorage.setItem(`booking${key.charAt(0).toUpperCase() + key.slice(1)}`, JSON.stringify(value));
    });

    // Navigate to checkout
    navigate('/checkout', { state: mockData });
  };

  const clearAllData = () => {
    console.log('🧹 Clearing all booking data...');
    
    const keys = [
      'bookingSearchData',
      'bookingSelectedFlight',
      'bookingSelectedOutboundFlight',
      'bookingSelectedReturnFlight',
      'bookingPassengers',
      'bookingEmail',
      'bookingTripType'
    ];
    
    keys.forEach(key => localStorage.removeItem(key));
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            🔍 Checkout Debug Page
          </h1>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                ❌ BookingContext Error
              </h3>
              <p className="text-red-600">{error}</p>
              <p className="text-sm text-red-500 mt-2">
                This is likely the cause of the checkout page crash.
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Location Info */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-blue-800 mb-3">
                📍 Location Info
              </h3>
              <div className="space-y-2 text-sm">
                <p><strong>Pathname:</strong> {debugInfo.location?.pathname}</p>
                <p><strong>Search:</strong> {debugInfo.location?.search || 'None'}</p>
                <p><strong>State:</strong> {debugInfo.location?.state ? 'Present' : 'None'}</p>
              </div>
            </div>

            {/* BookingContext Info */}
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-green-800 mb-3">
                🔗 BookingContext Info
              </h3>
              {debugInfo.contextError ? (
                <p className="text-red-600 text-sm">{debugInfo.contextError}</p>
              ) : (
                <div className="space-y-2 text-sm">
                  <p><strong>Loaded:</strong> {debugInfo.bookingContext?.isLoaded ? 'Yes' : 'No'}</p>
                  <p><strong>Flight:</strong> {debugInfo.bookingContext?.selectedFlight ? 'Present' : 'None'}</p>
                  <p><strong>Passengers:</strong> {debugInfo.bookingContext?.passengers || 0}</p>
                  <p><strong>Email:</strong> {debugInfo.bookingContext?.email ? 'Present' : 'None'}</p>
                  <p><strong>Trip Type:</strong> {debugInfo.bookingContext?.tripType || 'None'}</p>
                </div>
              )}
            </div>
          </div>

          {/* LocalStorage Data */}
          <div className="bg-yellow-50 rounded-lg p-4 mb-8">
            <h3 className="text-lg font-semibold text-yellow-800 mb-3">
              💾 LocalStorage Data
            </h3>
            <div className="text-sm space-y-2">
              {Object.entries(debugInfo.localStorage || {}).map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="font-medium">{key}:</span>
                  <span className="text-gray-600">
                    {value ? (typeof value === 'object' ? 'Object' : 'Present') : 'None'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4">
            <button
              onClick={simulateCheckoutNavigation}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              🧪 Simulate Checkout Navigation
            </button>
            
            <button
              onClick={() => navigate('/checkout')}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
            >
              🚀 Go to Checkout (Direct)
            </button>
            
            <button
              onClick={() => navigate('/checkout?test=true')}
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
            >
              🧪 Go to Checkout (Test Mode)
            </button>
            
            <button
              onClick={clearAllData}
              className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
            >
              🧹 Clear All Data
            </button>
            
            <button
              onClick={() => navigate('/')}
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
            >
              🏠 Back to Home
            </button>
          </div>

          {/* Raw Debug Data */}
          <div className="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">
              🔧 Raw Debug Data
            </h3>
            <pre className="text-xs text-gray-600 overflow-auto max-h-64">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutDebugPage;
