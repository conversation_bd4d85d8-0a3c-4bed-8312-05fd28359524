import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast, ToastContainer } from 'react-toastify';
import {
  CreditCardIcon,
  LockClosedIcon,
  ShieldCheckIcon,
  ClockIcon,
  DocumentCheckIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  GlobeAltIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';

// Import utilities and context with error handling
let useBooking, getCheckoutData, paymentAPI, generateReservationCode;

try {
  ({ useBooking } = require('../context/BookingContext'));
  ({ getCheckoutData } = require('../utils/sessionStorageHelper'));
  ({ paymentAPI } = require('../utils/paymentAPI'));
  ({ generateReservationCode } = require('../utils/reservationUtils'));
} catch (importError) {
  console.error('❌ Import error in CheckoutPageRobustFixed:', importError);
}

const CheckoutPageRobustFixed = () => {
  console.log('🔍 CheckoutPageRobustFixed: Premium checkout component initializing...');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('stripe');
  const [error, setError] = useState(null);
  const [bookingData, setBookingData] = useState(null);
  const [renderKey, setRenderKey] = useState(0);

  // Debug effect to monitor bookingData changes
  useEffect(() => {
    console.log('🔍 BookingData State Changed:');
    console.log('  - bookingData:', bookingData);
    console.log('  - selectedFlight:', bookingData?.selectedFlight);
    console.log('  - selectedOutboundFlight:', bookingData?.selectedOutboundFlight);
    console.log('  - selectedReturnFlight:', bookingData?.selectedReturnFlight);
    console.log('  - tripType:', bookingData?.tripType);
    console.log('  - isLoading:', isLoading);

    // Test price calculation immediately when data changes
    if (bookingData) {
      const price = calculateTotalPrice();
      console.log('🔍 Calculated price with current data:', price);
    }
  }, [bookingData, isLoading]);

  // Navigation hooks
  const navigate = useNavigate();
  const location = useLocation();

  // Utility functions (defined early to avoid hoisting issues)
  const calculateTotalPrice = () => {
    if (!bookingData) return 4.99;

    console.log('🔍 calculateTotalPrice: Checking booking data:', {
      tripType: bookingData.tripType,
      selectedFlight: !!bookingData.selectedFlight,
      selectedOutboundFlight: !!bookingData.selectedOutboundFlight,
      selectedReturnFlight: !!bookingData.selectedReturnFlight
    });

    // Calculate price based on trip type and actual flight selections
    const tripType = bookingData.tripType || 'oneWay';

    if (tripType === 'return' || tripType === 'roundTrip') {
      // For return trips, check if we have both outbound and return flights
      const hasOutbound = bookingData.selectedOutboundFlight || bookingData.selectedFlight;
      const hasReturn = bookingData.selectedReturnFlight;

      console.log('🔍 calculateTotalPrice: Return trip - hasOutbound:', !!hasOutbound, 'hasReturn:', !!hasReturn);

      if (hasOutbound && hasReturn) {
        console.log('✅ calculateTotalPrice: Return trip with both flights - $9.98');
        return 9.98;
      } else if (hasOutbound) {
        console.log('⚠️ calculateTotalPrice: Return trip with only outbound flight - $4.99');
        return 4.99;
      }
    }

    // One-way trip or fallback
    console.log('✅ calculateTotalPrice: One-way trip - $4.99');
    return 4.99;
  };

  // Safely get booking context data
  const getBookingContextData = () => {
    try {
      if (!useBooking) {
        throw new Error('useBooking hook not available');
      }
      
      const contextData = useBooking();
      console.log('✅ BookingContext data retrieved:', contextData);
      return contextData;
    } catch (err) {
      console.error('❌ BookingContext error:', err);
      return null;
    }
  };

  // Initialize booking data with multiple fallback strategies
  useEffect(() => {
    console.log('🔍 CheckoutPageRobustFixed: Initializing booking data...');
    console.log('🔍 Current location:', location);
    console.log('🔍 URL search params:', location.search);

    const initializeBookingData = async () => {
      try {
        let finalBookingData = null;

        // Strategy 1: Try to get data from BookingContext
        console.log('🔍 Strategy 1: Checking BookingContext...');
        const contextData = getBookingContextData();
        console.log('🔍 BookingContext data:', contextData);
        // Check for any valid flight data (selectedFlight, selectedOutboundFlight, or both outbound+return)
        if (contextData && (contextData.selectedFlight || contextData.selectedOutboundFlight)) {
          console.log('✅ Using BookingContext data');
          console.log('✅ BookingContext selectedFlight:', contextData.selectedFlight);
          console.log('✅ BookingContext selectedOutboundFlight:', contextData.selectedOutboundFlight);
          console.log('✅ BookingContext selectedReturnFlight:', contextData.selectedReturnFlight);
          console.log('✅ BookingContext tripType:', contextData.tripType);
          finalBookingData = contextData;
        } else {
          console.log('❌ BookingContext data invalid or missing flight data');
        }

        // Strategy 2: Try to get data from navigation state
        if (!finalBookingData && location.state) {
          console.log('🔍 Strategy 2: Checking navigation state...');
          console.log('🔍 Navigation state:', location.state);

          // Check for flight data in navigation state (SearchResultsPage format)
          const hasFlightData = location.state.selectedFlight ||
                               location.state.selectedOutboundFlight ||
                               location.state.flight ||
                               location.state.outboundFlight;

          if (hasFlightData) {
            console.log('✅ Using navigation state data');

            // Map SearchResultsPage navigation state format to BookingContext format
            const mappedData = {
              ...location.state,
              tripType: location.state.tripType || 'oneWay'
            };

            // Handle one-way flight mapping
            if (location.state.flight && !location.state.selectedFlight) {
              mappedData.selectedFlight = location.state.flight;
              console.log('✅ Mapped navigation flight to selectedFlight');
            }

            // Handle return flight mapping
            if (location.state.outboundFlight && !location.state.selectedOutboundFlight) {
              mappedData.selectedOutboundFlight = location.state.outboundFlight;
              console.log('✅ Mapped navigation outboundFlight to selectedOutboundFlight');
            }

            if (location.state.returnFlight && !location.state.selectedReturnFlight) {
              mappedData.selectedReturnFlight = location.state.returnFlight;
              console.log('✅ Mapped navigation returnFlight to selectedReturnFlight');
            }

            console.log('✅ Final mapped navigation data:', {
              tripType: mappedData.tripType,
              selectedFlight: !!mappedData.selectedFlight,
              selectedOutboundFlight: !!mappedData.selectedOutboundFlight,
              selectedReturnFlight: !!mappedData.selectedReturnFlight
            });

            finalBookingData = mappedData;
          } else {
            console.log('❌ Navigation state missing flight data');
          }
        } else {
          console.log('🔍 Strategy 2: No navigation state available');
        }

        // Strategy 3: Try to get data from sessionStorage
        if (!finalBookingData && getCheckoutData) {
          console.log('🔍 Strategy 3: Checking session storage...');
          try {
            const sessionData = getCheckoutData();
            console.log('🔍 Session storage data:', sessionData);
            // Check for any valid flight data in session storage
            if (sessionData && (sessionData.selectedFlight || sessionData.selectedOutboundFlight)) {
              console.log('✅ Using session storage data');
              console.log('✅ Session selectedFlight:', sessionData.selectedFlight);
              console.log('✅ Session selectedOutboundFlight:', sessionData.selectedOutboundFlight);
              console.log('✅ Session selectedReturnFlight:', sessionData.selectedReturnFlight);
              finalBookingData = sessionData;
            } else {
              console.log('❌ Session storage data invalid or missing flight data');
            }
          } catch (sessionError) {
            console.warn('⚠️ Session storage error:', sessionError);
          }
        } else {
          console.log('🔍 Strategy 3: getCheckoutData not available');
        }

        // Strategy 4: Check URL parameters for test mode
        const urlParams = new URLSearchParams(location.search);
        const isTestMode = urlParams.get('test') === 'true' || urlParams.get('bypass') === 'true';
        console.log('🔍 Strategy 4: Test mode check - isTestMode:', isTestMode);

        if (!finalBookingData && isTestMode) {
          console.log('✅ Using test mode data');
          const testData = createTestModeData();
          console.log('✅ Test mode data created:', testData);
          console.log('✅ Test selectedFlight:', testData.selectedFlight);
          finalBookingData = testData;
        } else if (!finalBookingData) {
          console.log('🔍 Strategy 4: Not in test mode or already have data');
        }

        // Strategy 5: Create minimal fallback data
        if (!finalBookingData) {
          console.log('⚠️ Strategy 5: No data found, creating fallback data');
          const fallbackData = createFallbackData();
          console.log('⚠️ Fallback data created:', fallbackData);
          console.log('⚠️ Fallback selectedFlight:', fallbackData.selectedFlight);
          finalBookingData = fallbackData;
        }

        // Validate and set the booking data
        if (finalBookingData) {
          console.log('✅ Final booking data to be set:', finalBookingData);
          console.log('✅ Flight data summary:');
          console.log('  - tripType:', finalBookingData.tripType);
          console.log('  - selectedFlight:', !!finalBookingData.selectedFlight);
          console.log('  - selectedOutboundFlight:', !!finalBookingData.selectedOutboundFlight);
          console.log('  - selectedReturnFlight:', !!finalBookingData.selectedReturnFlight);

          if (finalBookingData.selectedFlight) {
            console.log('  - selectedFlight details:', {
              airline: finalBookingData.selectedFlight.airline,
              flightNumber: finalBookingData.selectedFlight.flightNumber,
              departure: finalBookingData.selectedFlight.departure,
              arrival: finalBookingData.selectedFlight.arrival
            });
          }

          if (finalBookingData.selectedOutboundFlight) {
            console.log('  - selectedOutboundFlight details:', {
              airline: finalBookingData.selectedOutboundFlight.airline,
              flightNumber: finalBookingData.selectedOutboundFlight.flightNumber
            });
          }

          if (finalBookingData.selectedReturnFlight) {
            console.log('  - selectedReturnFlight details:', {
              airline: finalBookingData.selectedReturnFlight.airline,
              flightNumber: finalBookingData.selectedReturnFlight.flightNumber
            });
          }

          setBookingData(finalBookingData);
          setRenderKey(prev => prev + 1);
          console.log('✅ Booking data state updated successfully');
        } else {
          throw new Error('Unable to initialize booking data');
        }

      } catch (initError) {
        console.error('❌ Booking data initialization failed:', initError);
        setError(`Initialization Error: ${initError.message}`);
        const fallbackData = createFallbackData();
        console.log('❌ Setting fallback data due to error:', fallbackData);
        setBookingData(fallbackData);
        setRenderKey(prev => prev + 1);
      } finally {
        setIsLoading(false);
        console.log('✅ Booking data initialization complete');
      }
    };

    initializeBookingData();
  }, [location.search, location.pathname]);

  // Create test mode data
  const createTestModeData = () => {
    const urlParams = new URLSearchParams(location.search);
    const tripType = urlParams.get('tripType') || 'oneWay';

    console.log('🔍 createTestModeData: Creating test data for tripType:', tripType);

    const baseData = {
      passengers: [
        { id: 1, firstName: 'John', lastName: 'Smith' }
      ],
      email: '<EMAIL>',
      tripType: tripType
    };

    if (tripType === 'return') {
      // For return trips, create separate outbound and return flights
      const testData = {
        ...baseData,
        selectedOutboundFlight: {
          id: 'test-outbound-123',
          airline: 'Emirates',
          flightNumber: 'EK205',
          departure: {
            airport: 'JFK',
            city: 'New York',
            time: '10:00',
            date: '2025-02-15'
          },
          arrival: {
            airport: 'LHR',
            city: 'London',
            time: '22:00',
            date: '2025-02-15'
          },
          price: 4.99,
          duration: '8h 00m'
        },
        selectedReturnFlight: {
          id: 'test-return-456',
          airline: 'Emirates',
          flightNumber: 'EK206',
          departure: {
            airport: 'LHR',
            city: 'London',
            time: '14:00',
            date: '2025-02-20'
          },
          arrival: {
            airport: 'JFK',
            city: 'New York',
            time: '18:00',
            date: '2025-02-20'
          },
          price: 4.99,
          duration: '8h 00m'
        }
      };
      console.log('✅ createTestModeData: Created return trip data with outbound and return flights');
      return testData;
    } else {
      // For one-way trips, use selectedFlight
      const testData = {
        ...baseData,
        selectedFlight: {
          id: 'test-flight-123',
          airline: 'Emirates',
          flightNumber: 'EK205',
          departure: {
            airport: 'JFK',
            city: 'New York',
            time: '10:00',
            date: '2025-02-15'
          },
          arrival: {
            airport: 'LHR',
            city: 'London',
            time: '22:00',
            date: '2025-02-15'
          },
          price: 4.99,
          duration: '8h 00m'
        }
      };
      console.log('✅ createTestModeData: Created one-way trip data with selectedFlight');
      return testData;
    }
  };

  // Create fallback data
  const createFallbackData = () => ({
    selectedFlight: {
      id: 'fallback-flight',
      airline: 'KLM Royal Dutch Airlines',
      flightNumber: 'KL644',
      departure: {
        airport: 'JFK',
        city: 'New York',
        time: '10:00',
        date: '2025-02-15'
      },
      arrival: {
        airport: 'AMS',
        city: 'Amsterdam',
        time: '22:00',
        date: '2025-02-15'
      },
      price: 4.99,
      duration: '8h 00m'
    },
    passengers: [
      { id: 1, firstName: 'Sample', lastName: 'Passenger' }
    ],
    email: '<EMAIL>',
    tripType: 'oneWay'
  });

  // Payment handlers with comprehensive error handling
  const handleStripePayment = async () => {
    console.log('💳 Processing Stripe payment...');
    setIsProcessingPayment(true);

    try {
      if (!paymentAPI) {
        throw new Error('Payment API not available');
      }

      const paymentData = {
        amount: calculateTotalPrice(),
        currency: 'usd',
        bookingData: bookingData,
        paymentMethod: 'stripe'
      };

      const result = await paymentAPI.processStripePayment(paymentData);
      
      if (result.success) {
        const bookingReference = generateReservationCode ? generateReservationCode() : `VO${Date.now()}`;
        toast.success('Payment successful! Redirecting...');
        
        setTimeout(() => {
          navigate('/success', {
            state: {
              bookingReference,
              paymentId: result.paymentId,
              bookingData
            }
          });
        }, 1500);
      } else {
        throw new Error(result.error || 'Payment failed');
      }
    } catch (error) {
      console.error('❌ Stripe payment error:', error);
      toast.error(`Payment failed: ${error.message}`);
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handlePayPalPayment = async () => {
    console.log('💳 Processing PayPal payment...');
    setIsProcessingPayment(true);

    try {
      if (!paymentAPI) {
        throw new Error('Payment API not available');
      }

      const paymentData = {
        amount: calculateTotalPrice(),
        currency: 'usd',
        bookingData: bookingData,
        paymentMethod: 'paypal'
      };

      const result = await paymentAPI.processPayPalPayment(paymentData);
      
      if (result.success) {
        const bookingReference = generateReservationCode ? generateReservationCode() : `VO${Date.now()}`;
        toast.success('Payment successful! Redirecting...');
        
        setTimeout(() => {
          navigate('/success', {
            state: {
              bookingReference,
              paymentId: result.paymentId,
              bookingData
            }
          });
        }, 1500);
      } else {
        throw new Error(result.error || 'Payment failed');
      }
    } catch (error) {
      console.error('❌ PayPal payment error:', error);
      toast.error(`Payment failed: ${error.message}`);
    } finally {
      setIsProcessingPayment(false);
    }
  };



  const handleEditDetails = () => {
    navigate('/search', { state: { editMode: true, bookingData } });
  };

  // Premium Loading state
  if (isLoading) {
    console.log('🔍 Showing loading state - isLoading:', isLoading);
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-brand-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <div className="relative mb-8">
            <div className="w-16 h-16 border-4 border-brand-200 rounded-full animate-spin mx-auto"></div>
            <div className="w-16 h-16 border-4 border-brand-500 border-t-transparent rounded-full animate-spin absolute top-0 left-1/2 transform -translate-x-1/2"></div>
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-bold text-brand-900">Loading Checkout</h3>
            <p className="text-brand-600">Preparing your secure payment...</p>
          </div>
        </motion.div>
      </div>
    );
  }

  // Debug render state
  console.log('🔍 Main render - isLoading:', isLoading, 'bookingData:', bookingData);

  // Premium Error state
  if (error && !bookingData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-brand-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="card-aviation max-w-md w-full text-center"
        >
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-brand-900 mb-4">Checkout Unavailable</h2>
          <p className="text-brand-600 mb-8 leading-relaxed">{error}</p>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/')}
              className="btn-cta-primary w-full"
            >
              Start New Reservation
            </button>
            <button
              onClick={() => window.location.reload()}
              className="checkout-payment-button-secondary w-full"
            >
              Try Again
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div key={renderKey} className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-brand-50">


      <div className="container-modern py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-6xl mx-auto"
        >
          {/* Premium Page Header */}
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="mb-6"
            >
              <h1 className="heading-primary text-brand-900 mb-4">
                Complete Your Flight Reservation
              </h1>
              <p className="text-lg text-brand-600 max-w-2xl mx-auto">
                Secure, embassy-approved flight reservations delivered instantly
              </p>
            </motion.div>

            {/* Premium Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap items-center justify-center gap-4 mb-8"
            >
              <div className="trust-badge">
                <ShieldCheckIcon className="w-5 h-5 mr-2" />
                SSL Secured
              </div>
              <div className="trust-badge-green">
                <DocumentCheckIcon className="w-5 h-5 mr-2" />
                Embassy Approved
              </div>
              <div className="trust-badge-gold">
                <ClockIcon className="w-5 h-5 mr-2" />
                Instant Delivery
              </div>
              <div className="trust-badge-purple">
                <GlobeAltIcon className="w-5 h-5 mr-2" />
                Real Flight Data
              </div>
            </motion.div>


          </div>

          {/* Streamlined Payment Layout */}
          <div className="max-w-2xl mx-auto">
            {/* Payment Section */}
            <div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="checkout-payment-container"
              >
                {/* Payment Header */}
                <div className="checkout-payment-header">
                  <div className="icon">
                    <LockClosedIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="title">Secure Payment</div>
                    <div className="lock-badge">
                      <ShieldCheckIcon className="w-4 h-4 mr-1" />
                      SSL Protected
                    </div>
                  </div>
                </div>

                {/* Price Display */}
                <div className="checkout-price-display">
                  <div className="amount">${calculateTotalPrice()}</div>
                  <div className="description">Embassy-Approved Flight Reservation</div>
                </div>

                {/* Trust Badges */}
                <div className="checkout-trust-badges">
                  <div className="checkout-trust-badge ssl">
                    <ShieldCheckIcon className="w-4 h-4 mr-1" />
                    SSL Secured
                  </div>
                  <div className="checkout-trust-badge embassy">
                    <DocumentCheckIcon className="w-4 h-4 mr-1" />
                    Embassy Approved
                  </div>
                  <div className="checkout-trust-badge instant">
                    <ClockIcon className="w-4 h-4 mr-1" />
                    Instant Delivery
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="space-y-4 mb-8">
                  <h4 className="text-lg font-bold text-brand-900">Choose Payment Method</h4>

                  <div className="space-y-3">
                    <label className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 ${
                      paymentMethod === 'stripe'
                        ? 'border-brand-500 bg-brand-50 shadow-aviation'
                        : 'border-brand-200 hover:border-brand-300 hover:bg-brand-50'
                    }`}>
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="stripe"
                        checked={paymentMethod === 'stripe'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="w-5 h-5 text-brand-600 mr-4"
                      />
                      <CreditCardIcon className="h-6 w-6 text-brand-600 mr-3" />
                      <div>
                        <div className="font-semibold text-brand-900">Credit/Debit Card</div>
                        <div className="text-sm text-brand-600">Visa, Mastercard, Amex</div>
                      </div>
                    </label>

                    <label className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 ${
                      paymentMethod === 'paypal'
                        ? 'border-yellow-500 bg-yellow-50 shadow-aviation'
                        : 'border-brand-200 hover:border-brand-300 hover:bg-brand-50'
                    }`}>
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="paypal"
                        checked={paymentMethod === 'paypal'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="w-5 h-5 text-yellow-600 mr-4"
                      />
                      <BanknotesIcon className="h-6 w-6 text-yellow-600 mr-3" />
                      <div>
                        <div className="font-semibold text-brand-900">PayPal</div>
                        <div className="text-sm text-brand-600">Pay with your PayPal account</div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Payment Buttons */}
                <div className="space-y-4">
                  {paymentMethod === 'stripe' ? (
                    <motion.button
                      onClick={handleStripePayment}
                      disabled={isProcessingPayment}
                      className="checkout-payment-button"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {isProcessingPayment ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                          Processing Payment...
                        </div>
                      ) : (
                        <div className="flex items-center justify-center">
                          <CreditCardIcon className="h-5 w-5 mr-3" />
                          Pay ${calculateTotalPrice()} with Card
                        </div>
                      )}
                    </motion.button>
                  ) : (
                    <motion.button
                      onClick={handlePayPalPayment}
                      disabled={isProcessingPayment}
                      className="checkout-paypal-button"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {isProcessingPayment ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-3"></div>
                          Processing Payment...
                        </div>
                      ) : (
                        <div className="flex items-center justify-center">
                          <span className="paypal-logo mr-2">PayPal</span>
                          ${calculateTotalPrice()}
                        </div>
                      )}
                    </motion.button>
                  )}


                </div>

                {/* Security Footer */}
                <div className="text-center pt-6 border-t border-brand-100">
                  <div className="flex items-center justify-center text-sm text-brand-600 mb-2">
                    <LockClosedIcon className="h-4 w-4 mr-2" />
                    256-bit SSL encryption
                  </div>
                  <p className="text-xs text-brand-500">
                    Your payment information is secure and never stored
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Premium Toast Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={4000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        toastClassName="bg-white shadow-aviation border border-brand-100 rounded-xl"
        bodyClassName="text-brand-900 font-medium"
        progressClassName="bg-brand-500"
      />
    </div>
  );
};

export default CheckoutPageRobustFixed;
