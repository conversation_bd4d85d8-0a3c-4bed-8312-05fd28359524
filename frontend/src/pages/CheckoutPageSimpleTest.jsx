import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const CheckoutPageSimpleTest = () => {
  console.log('🚨 CheckoutPageSimpleTest: Component starting...');
  
  const [step, setStep] = useState(1);
  const [error, setError] = useState(null);
  
  // Test basic hooks
  const navigate = useNavigate();
  const location = useLocation();
  console.log('✅ Step 1: Basic hooks loaded');
  
  // Test useEffect
  useEffect(() => {
    console.log('✅ Step 2: useEffect running');
    setStep(2);
    
    // Test async operations
    const testAsync = async () => {
      try {
        console.log('✅ Step 3: Testing async operations...');
        await new Promise(resolve => setTimeout(resolve, 100));
        console.log('✅ Step 4: Async operations successful');
        setStep(3);
      } catch (err) {
        console.error('❌ Async error:', err);
        setError('Async operation failed');
      }
    };
    
    testAsync();
  }, []);
  
  if (error) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          <h1 className="text-xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-green-500 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Checkout Simple Test
        </h1>
        
        <div className="space-y-4">
          <div className={`p-3 rounded ${step >= 1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
            ✅ Step 1: Basic hooks loaded
          </div>
          
          <div className={`p-3 rounded ${step >= 2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
            {step >= 2 ? '✅' : '⏳'} Step 2: useEffect running
          </div>
          
          <div className={`p-3 rounded ${step >= 3 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
            {step >= 3 ? '✅' : '⏳'} Step 3: Async operations successful
          </div>
        </div>
        
        {step >= 3 && (
          <div className="mt-6 p-4 bg-blue-100 rounded">
            <p className="text-blue-800 font-medium">🎉 All basic functionality working!</p>
            <p className="text-blue-600 text-sm mt-1">Ready to test BookingContext</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CheckoutPageSimpleTest;
