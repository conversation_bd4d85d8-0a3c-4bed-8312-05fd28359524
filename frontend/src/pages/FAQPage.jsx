import React, { useEffect, useState, useMemo } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';

import FloatingCTA from '../components/FloatingCTA';

const FAQPage = () => {
  const [openIndex, setOpenIndex] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();


  // Set page title and meta description for SEO optimization
  useEffect(() => {
    document.title = 'FAQ - Embassy-Approved Flight Reservations | VerifiedOnward';

    // Update meta description for AI and Google search optimization
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Get expert answers about embassy-approved flight reservations, dummy tickets, and visa applications. Trusted by 75,000+ travelers worldwide. $4.99 instant download.';

    // Add FAQ schema markup for rich snippets
    let faqSchema = document.querySelector('script[type="application/ld+json"]');
    if (!faqSchema) {
      faqSchema = document.createElement('script');
      faqSchema.type = 'application/ld+json';
      document.head.appendChild(faqSchema);
    }

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
      // Remove FAQ schema when leaving page
      const schemaElement = document.querySelector('script[type="application/ld+json"]');
      if (schemaElement) {
        schemaElement.remove();
      }
    };
  }, []);

  const faqCategories = [
    {
      title: "Getting Started",
      icon: "🚀",
      color: "brand",
      faqs: [
        {
          question: "What are visa-ready flight reservations?",
          answer: "Visa-ready flight reservations are professional airline bookings that show your travel itinerary without requiring expensive ticket purchases. VerifiedOnward creates authentic reservations using real airline data, trusted by immigration offices in 195+ countries worldwide. These reservations meet common visa requirements while saving you from costly non-refundable tickets that can cost $500+.",
          keywords: ["visa-ready", "flight reservations", "visa applications", "authentic", "professional", "dummy ticket"]
        },
        {
          question: "How do I get my embassy-approved flight reservation?",
          answer: "Getting your reservation takes just 60 seconds with our streamlined 3-step process: Search flights using real-time airline data, enter passenger details (up to 2 travelers), pay the one-time $4.99 fee, and instantly download your professional embassy-ready document. No waiting, no complications.",
          keywords: ["3-step process", "60 seconds", "2 travelers", "$4.99", "instant download", "embassy-ready"]
        },
        {
          question: "Will my flight reservation be accepted by all embassies?",
          answer: "Yes! Our flight reservations are specifically designed to meet visa requirements in 195+ countries worldwide. We use authentic airline data and professional formatting that matches real airline reservations, ensuring visa acceptance for all visa application types including Schengen, UK, US, Canada, and Australia.",
          keywords: ["visa acceptance", "195+ countries", "authentic airline data", "visa applications", "Schengen", "UK", "US"]
        },
        {
          question: "Can I use this for any type of visa application?",
          answer: "Absolutely! Our embassy-approved reservations work for all visa types including tourist, business, student, family visit, and transit visas. Whether you're applying for Schengen, UK, US, Canada, Australia, or any other country's visa, our reservations meet the required documentation standards.",
          keywords: ["all visa types", "Schengen", "UK", "US", "Canada", "Australia", "tourist", "business", "student", "visa flight reservation"]
        }
      ]
    },
    {
      title: "Service Details",
      icon: "✈️",
      color: "accent",
      faqs: [
        {
          question: "Does my reservation show a real flight itinerary?",
          answer: "Absolutely! Your VerifiedOnward reservation displays authentic flight data from global airline systems, including airline name, flight number, departure/arrival times, aircraft type, and duration. The result is a professional, embassy-approved flight reservation with verifiable booking reference that meets all visa application requirements.",
          keywords: ["authentic flight data", "airline name", "flight number", "verifiable booking reference", "embassy-approved", "real flight itinerary"]
        },
        {
          question: "Does my reservation show a valid booking reference?",
          answer: "Yes! VerifiedOnward provides authentic airline reservations with verifiable booking references that embassies can validate. Our service is embassy-trusted and immigration-safe because the reservations follow official airline formatting standards used by visa officers worldwide.",
          keywords: ["authentic airline reservations", "embassy-trusted", "immigration-safe", "official airline formatting", "valid booking reference"]
        },
        {
          question: "What can I use VerifiedOnward flight reservations for?",
          answer: "Our embassy-approved reservations are perfect for visa applications requiring flight itinerary proof, demonstrating onward/return travel intent, hotel check-ins requiring travel documentation, immigration interviews, travel insurance applications, and any situation needing verifiable flight booking documentation.",
          keywords: ["visa applications", "onward travel", "return travel", "immigration interviews", "verifiable documentation", "flight itinerary proof"]
        }
      ]
    },
    {
      title: "Booking & Payment",
      icon: "💳",
      color: "brand",
      faqs: [
        {
          question: "Can I add multiple passengers to my reservation?",
          answer: "Yes! You can add up to 2 passengers to a single flight reservation at no extra cost. Simply enter their names during booking and all travelers will appear on one shared embassy-approved reservation document. This is perfect for couples, families, or business partners applying for visas together.",
          keywords: ["multiple passengers", "2 passengers", "no extra cost", "embassy-approved", "couples", "families"]
        },
        {
          question: "How will I receive my flight reservation?",
          answer: "Immediately after payment, your professional flight reservation will be available for instant download on the confirmation page. A backup copy is automatically sent to your email within 60 seconds. We recommend saving the document to multiple locations for your visa application.",
          keywords: ["instant download", "confirmation page", "email backup", "60 seconds", "visa application", "professional"]
        },
        {
          question: "What if I didn't receive my flight reservation?",
          answer: "No worries! If you didn't receive your reservation file or accidentally deleted it, simply email <NAME_EMAIL> with your order ID or payment receipt. Our support team will resend your reservation promptly within hours.",
          keywords: ["didn't receive", "<EMAIL>", "order ID", "payment receipt", "resend", "support team"]
        },
        {
          question: "How long is my flight reservation valid?",
          answer: "Your flight reservation remains valid for the duration shown on the document, typically 24-48 hours from creation. This provides sufficient time for your visa application submission. If you need a longer validity period for specific embassy requirements, please contact our support team.",
          keywords: ["flight reservation valid", "24-48 hours", "visa application submission", "embassy requirements", "validity period"]
        },
        {
          question: "Is it legal to use flight reservations for visa applications?",
          answer: "Yes, it's completely legal and widely accepted practice. Many embassies and consulates actually recommend using flight reservations rather than purchasing expensive tickets for visa applications. Our service provides legitimate flight reservations that meet all legal and embassy requirements.",
          keywords: ["legal", "widely accepted", "embassies recommend", "expensive tickets", "legitimate", "embassy requirements"]
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit cards (Visa, Mastercard, American Express), debit cards, and secure online payment methods through our encrypted payment gateway. All transactions are processed securely with industry-standard SSL encryption to protect your financial information.",
          keywords: ["credit cards", "Visa", "Mastercard", "American Express", "debit cards", "encrypted payment", "SSL encryption"]
        },
        {
          question: "Can I use the same reservation for multiple visa applications?",
          answer: "Yes, you can use the same flight reservation document for multiple visa applications as long as it remains within the validity period. However, we recommend generating a fresh reservation for each application to ensure current flight information and avoid potential embassy processing issues.",
          keywords: ["same reservation", "multiple visa applications", "validity period", "fresh reservation", "current flight information", "embassy processing"]
        }
      ]
    },
    {
      title: "Advanced Questions",
      icon: "🔍",
      color: "accent",
      faqs: [
        {
          question: "Can embassies verify my dummy ticket online?",
          answer: "Visa offices cannot verify dummy tickets through airline websites since they're created for documentation purposes only. However, VerifiedOnward provides professional flight reservations with authentic airline data that meet common visa requirements. Our reservations are designed specifically for visa applications and are accepted by immigration offices worldwide.",
          keywords: ["embassies verify", "dummy ticket", "airline websites", "documentation purposes", "authentic airline data", "immigration offices"]
        },
        {
          question: "What happens if my visa is delayed and my ticket expires?",
          answer: "If your visa processing is delayed beyond your reservation's validity period, simply contact our support <NAME_EMAIL>. We can provide guidance on generating a new reservation if needed. Many embassies accept reservations that were valid at the time of application submission.",
          keywords: ["visa delayed", "ticket expires", "validity period", "support team", "new reservation", "application submission"]
        },
        {
          question: "Why choose VerifiedOnward over free dummy ticket generators?",
          answer: "VerifiedOnward provides professional, embassy-approved reservations with authentic airline data and verifiable booking references. Free generators often produce low-quality documents that may be rejected by embassies. Our $4.99 service ensures embassy acceptance, professional formatting, and expert support for your visa application success.",
          keywords: ["VerifiedOnward", "free dummy ticket generators", "embassy-approved", "authentic airline data", "embassy acceptance", "professional formatting", "expert support"]
        }
      ]
    }
  ];

  // Keep categories structured for grouped display
  const allFaqs = faqCategories.flatMap(category =>
    category.faqs.map(faq => ({
      ...faq,
      category: category.title,
      icon: category.icon,
      color: category.color || 'brand'
    }))
  );

  // Group FAQs by category for structured display
  const groupFAQsByCategory = (faqs) => {
    const grouped = {};
    faqs.forEach(faq => {
      if (!grouped[faq.category]) {
        grouped[faq.category] = {
          title: faq.category,
          icon: faq.icon,
          color: faq.color,
          faqs: []
        };
      }
      grouped[faq.category].faqs.push(faq);
    });
    return Object.values(grouped);
  };

  // Generate FAQ Schema for SEO and AI search optimization
  const generateFAQSchema = () => {
    const faqSchema = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": allFaqs.map(faq => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer.replace(/\*\*/g, '') // Remove markdown formatting for schema
        }
      }))
    };
    return JSON.stringify(faqSchema);
  };

  // Update FAQ schema when FAQs change
  useEffect(() => {
    const faqSchema = document.querySelector('script[type="application/ld+json"]');
    if (faqSchema) {
      faqSchema.textContent = generateFAQSchema();
    }
  }, [allFaqs]);

  // Filter FAQs based on search query and group by category
  const filteredFaqCategories = useMemo(() => {
    if (!searchQuery.trim()) {
      return faqCategories;
    }

    const query = searchQuery.toLowerCase();
    const filteredCategories = faqCategories.map(category => ({
      ...category,
      faqs: category.faqs.filter(faq => {
        const questionMatch = faq.question.toLowerCase().includes(query);
        const answerMatch = faq.answer.toLowerCase().includes(query);
        const keywordMatch = faq.keywords?.some(keyword =>
          keyword.toLowerCase().includes(query)
        );
        const categoryMatch = category.title.toLowerCase().includes(query);
        return questionMatch || answerMatch || keywordMatch || categoryMatch;
      })
    })).filter(category => category.faqs.length > 0);

    return filteredCategories;
  }, [faqCategories, searchQuery]);

  // Get total filtered FAQ count for search results
  const totalFilteredFaqs = filteredFaqCategories.reduce((total, category) => total + category.faqs.length, 0);

  const toggleFAQ = (index) => {
    try {
      setOpenIndex(openIndex === index ? null : index);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('Error toggling FAQ:', error);
      setError('Failed to toggle FAQ. Please try again.');
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setOpenIndex(null);
  };

  const handleSearchChange = (e) => {
    try {
      const value = e.target.value;
      setSearchQuery(value);
      setOpenIndex(null); // Close any open FAQ when searching
    } catch (error) {
      console.warn('Search input error:', error);
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      toggleFAQ(index);
    }
  };

  // Format answer text with bold highlights - with error handling
  const formatAnswer = (answer) => {
    try {
      if (!answer || typeof answer !== 'string') {
        return answer || '';
      }
      return answer.replace(/\*\*(.*?)\*\*/g, '<span class="faq-answer-highlight">$1</span>');
    } catch (error) {
      console.warn('Answer formatting error:', error);
      return answer || '';
    }
  };

  const ChevronIcon = ({ isOpen }) => (
    <motion.svg
      className="w-6 h-6 text-brand-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      animate={{ rotate: isOpen ? 180 : 0 }}
      transition={{ duration: 0.3 }}
      strokeWidth={2.5}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </motion.svg>
  );

  return (
    <div className="min-h-screen faq-page">


      {/* Premium Floating CTA */}
      <FloatingCTA />

      {/* Premium Aviation FAQ Header Section */}
      <section className="relative min-h-screen flex items-center aviation-gradient-hero overflow-hidden">
        {/* Premium Aviation Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/8 via-transparent to-accent-500/8"></div>
        <div className="absolute top-20 left-10 w-80 h-80 bg-brand-400/15 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-400/15 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-brand-300/10 to-accent-300/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>

        <div className="container-modern text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >


            <motion.h1
              className="aviation-hero-text mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              Frequently Asked{' '}
              <span className="bg-gradient-to-r from-accent-600 to-accent-500 bg-clip-text text-transparent block">
                Questions
              </span>
            </motion.h1>

            <motion.div
              className="max-w-4xl mx-auto mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-8">
                Everything you need to know about our professional, embassy-approved flight reservation service.
                <strong className="text-brand-800"> Get expert answers from 75,000+ successful visa applicants.</strong>
              </p>

              {/* Enhanced Search Bar */}
              <motion.div
                className="faq-search-container mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.6 }}
              >
                <div className="relative">
                  <svg
                    className="faq-search-icon w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <input
                    type="text"
                    id="faq-search"
                    placeholder="Search FAQs... (e.g., 'visa application', 'embassy', 'payment')"
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="faq-search-input"
                    aria-label="Search frequently asked questions"
                    aria-describedby="search-results-count"
                    autoComplete="off"
                    spellCheck="false"
                  />
                  {searchQuery && (
                    <button
                      onClick={clearSearch}
                      className="faq-clear-button"
                      aria-label="Clear search"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
                {searchQuery && (
                  <motion.p
                    id="search-results-count"
                    className="text-sm text-brand-600 mt-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    role="status"
                    aria-live="polite"
                  >
                    Found {totalFilteredFaqs} result{totalFilteredFaqs !== 1 ? 's' : ''} for "{searchQuery}"
                  </motion.p>
                )}
              </motion.div>
            </motion.div>



            {/* Enhanced Trust Metrics */}
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto mb-16"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.8 }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                whileHover={{
                  y: -4,
                  scale: 1.02,
                  boxShadow: "0 12px 32px -8px rgba(14, 165, 233, 0.3)"
                }}
                className="faq-metrics-card border-brand-200 group cursor-pointer"
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-brand-100 to-brand-50 rounded-xl flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300">
                    ❓
                  </div>
                </div>
                <div className="text-4xl font-black text-brand-600 mb-2 group-hover:scale-105 transition-transform duration-300">
                  15+
                </div>
                <div className="text-sm font-bold text-brand-700 mb-2">
                  Expert FAQ Categories
                </div>
                <div className="text-xs text-neutral-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  Comprehensive answers to all your questions
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 1.0 }}
                whileHover={{
                  y: -4,
                  scale: 1.02,
                  boxShadow: "0 12px 32px -8px rgba(251, 191, 36, 0.3)"
                }}
                className="faq-metrics-card border-accent-200 group cursor-pointer"
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-accent-100 to-accent-50 rounded-xl flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300">
                    👥
                  </div>
                </div>
                <div className="text-4xl font-black text-accent-600 mb-2 group-hover:scale-105 transition-transform duration-300">
                  75K+
                </div>
                <div className="text-sm font-bold text-accent-700 mb-2">
                  Trusted by Travelers
                </div>
                <div className="text-xs text-neutral-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  Successful visa applications worldwide
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                whileHover={{
                  y: -4,
                  scale: 1.02,
                  boxShadow: "0 12px 32px -8px rgba(14, 165, 233, 0.3)"
                }}
                className="faq-metrics-card border-green-200 group cursor-pointer"
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-100 to-green-50 rounded-xl flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300">
                    ✅
                  </div>
                </div>
                <div className="text-4xl font-black text-green-600 mb-2 group-hover:scale-105 transition-transform duration-300">
                  99.7%
                </div>
                <div className="text-sm font-bold text-green-700 mb-2">
                  Visa Success Rate
                </div>
                <div className="text-xs text-neutral-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  Trusted by visa officers worldwide
                </div>
              </motion.div>
            </motion.div>
          </motion.div>

            {/* FAQ Questions - moved directly after stats */}

          <motion.div
            className="max-w-5xl mx-auto space-y-6 pb-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {searchQuery && totalFilteredFaqs === 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-12"
              >
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-2xl font-bold text-neutral-700 mb-2">No results found</h3>
                <p className="text-neutral-600 mb-6">
                  Try searching for different terms like "visa", "embassy", "payment", or "booking"
                </p>
                <button
                  onClick={clearSearch}
                  className="premium-button"
                >
                  Clear Search
                </button>
              </motion.div>
            )}

            {filteredFaqCategories.map((category, categoryIndex) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
                className="mb-12"
              >
                {/* Category Header */}
                <motion.div
                  className="mb-8 pb-4 border-b-2 border-gradient-to-r from-brand-200 to-accent-200"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 flex items-center gap-4 mb-2">
                    <span className="text-3xl" role="img" aria-label={`${category.title} category`}>
                      {category.icon}
                    </span>
                    {category.title}
                  </h2>
                  <div className="w-16 h-1 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full"></div>
                </motion.div>

                {/* Questions in this category */}
                <div className="space-y-4">
                  {category.faqs.map((faq, faqIndex) => {
                    const globalIndex = filteredFaqCategories.slice(0, categoryIndex).reduce((acc, cat) => acc + cat.faqs.length, 0) + faqIndex;
                    return (
                      <motion.div
                        key={`${faq.question}-${globalIndex}`}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: faqIndex * 0.05 }}
                        className="faq-accordion-item group"
                      >

                        <button
                          onClick={() => toggleFAQ(globalIndex)}
                          onKeyDown={(e) => handleKeyDown(e, globalIndex)}
                          className="faq-accordion-button group"
                          aria-expanded={openIndex === globalIndex}
                          aria-controls={`faq-answer-${globalIndex}`}
                          type="button"
                        >
                          <div className="flex items-start gap-4 flex-1">
                            <motion.div
                              className="faq-accordion-icon"
                              whileHover={{
                                scale: 1.1,
                                rotate: 5,
                                boxShadow: "0 8px 25px -8px rgba(14, 165, 233, 0.4)"
                              }}
                              transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </motion.div>
                            <h3 className="faq-question-text text-left">
                              {faq.question}
                            </h3>
                          </div>
                          <motion.div
                            animate={{ rotate: openIndex === globalIndex ? 180 : 0 }}
                            transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                            className="flex-shrink-0 ml-4"
                          >
                            <div className="w-8 h-8 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full flex items-center justify-center shadow-glow group-hover:shadow-luxury transition-all duration-300">
                              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                              </svg>
                            </div>
                          </motion.div>
                        </button>

                        <AnimatePresence>
                          {openIndex === globalIndex && (
                            <motion.div
                              id={`faq-answer-${globalIndex}`}
                              initial={{ height: 0, opacity: 0, y: -10 }}
                              animate={{ height: "auto", opacity: 1, y: 0 }}
                              exit={{ height: 0, opacity: 0, y: -10 }}
                              transition={{
                                duration: 0.4,
                                ease: [0.4, 0, 0.2, 1],
                                opacity: { duration: 0.3 }
                              }}
                              className="overflow-hidden"
                            >
                              <div className="faq-answer-content">
                                <div className="border-t border-gradient-to-r from-brand-200 to-accent-200 pt-6">
                                  <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.1, duration: 0.3 }}
                                    className="prose prose-lg max-w-none"
                                    dangerouslySetInnerHTML={{
                                      __html: formatAnswer(faq.answer)
                                    }}
                                  />

                                  {/* Keywords for better searchability */}
                                  {faq.keywords && faq.keywords.length > 0 && (
                                    <motion.div
                                      initial={{ opacity: 0, y: 10 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      transition={{ delay: 0.2, duration: 0.3 }}
                                      className="mt-6 pt-4 border-t border-neutral-100"
                                    >
                                      <p className="text-sm text-neutral-500 mb-2">Related topics:</p>
                                      <div className="flex flex-wrap gap-2">
                                        {faq.keywords.map((keyword, keyIndex) => (
                                          <span
                                            key={keyIndex}
                                            className="inline-flex items-center px-2 py-1 bg-brand-50 text-brand-600 rounded-md text-xs font-medium border border-brand-100"
                                          >
                                            {keyword}
                                          </span>
                                        ))}
                                      </div>
                                    </motion.div>
                                  )}
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        {/* Premium shine effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-2xl"></div>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Enhanced Support CTA Section */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>
        <div className="absolute top-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-72 h-72 bg-brand-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="max-w-4xl mx-auto"
          >
            <div className="faq-support-section">
              {/* Premium background effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000 rounded-3xl"></div>

              <div className="relative z-10">
                {/* Support Icon */}
                <motion.div
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 200, delay: 0.4 }}
                  className="w-16 h-16 bg-gradient-to-r from-brand-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-luxury"
                >
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </motion.div>

                <motion.h3
                  className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  💡 Still Have Questions?
                </motion.h3>

                <motion.p
                  className="text-xl text-neutral-600 mb-8 max-w-2xl mx-auto leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  Our expert support team is here to guide you through your visa process.
                  <strong className="text-neutral-800"> Get personalized email assistance from our professionals.</strong>
                </motion.p>

                {/* Dual CTA Buttons */}
                <motion.div
                  className="flex flex-col sm:flex-row justify-center items-center gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                >
                  <Link
                    className="faq-cta-primary group w-full sm:w-auto"
                    {...getCTANavigationProps(navigate, location.pathname)}
                  >
                    <motion.svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      whileHover={{ rotate: 15 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </motion.svg>
                    ✈️ Start Your Reservation
                    <motion.svg
                      className="w-5 h-5 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      whileHover={{ x: 4 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </motion.svg>
                  </Link>

                  <a
                    href="mailto:<EMAIL>"
                    className="faq-cta-secondary group w-full sm:w-auto"
                  >
                    <motion.svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      whileHover={{ scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </motion.svg>
                    📩 Email Support Team
                    <span className="text-xs opacity-75 ml-2">(Professional assistance)</span>
                  </a>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>


    </div>
  );
};

export default FAQPage;
