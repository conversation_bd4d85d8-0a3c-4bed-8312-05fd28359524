import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useBooking } from '../context/BookingContext';
import { ToastNotification } from '../components/SuccessMessage';
import { generateReservationCode } from '../utils/reservationCodeGenerator';

const SuccessPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isDownloading, setIsDownloading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  // Get booking data from navigation state and context
  const bookingContext = useBooking();
  const navigationData = location.state || {};

  // Handle test data from URL parameters
  const urlParams = new URLSearchParams(location.search);
  const isTestMode = urlParams.get('test') === 'true';
  const testData = isTestMode ? JSON.parse(urlParams.get('data') || '{}') : {};

  // Merge test data if in test mode
  const finalNavigationData = isTestMode ? { ...navigationData, ...testData } : navigationData;

  // Extract booking data with fallbacks
  const {
    selectedFlight = finalNavigationData.selectedFlight || finalNavigationData.flight,
    selectedOutboundFlight = finalNavigationData.selectedOutboundFlight || finalNavigationData.outboundFlight,
    selectedReturnFlight = finalNavigationData.selectedReturnFlight || finalNavigationData.returnFlight,
    passengers = finalNavigationData.passengers || bookingContext.passengers || [],
    email = finalNavigationData.email || bookingContext.email || '',
    paymentId = finalNavigationData.paymentData?.paymentId || finalNavigationData.paymentId || bookingContext.paymentId || '',
    bookingReference = finalNavigationData.bookingReference || bookingContext.bookingReference || '',
    tripType = finalNavigationData.tripType || (selectedReturnFlight ? 'return' : 'oneWay'),
    totalPrice = finalNavigationData.totalPrice || (tripType === 'return' ? '9.98' : '4.99')
  } = { ...bookingContext, ...finalNavigationData };

  // Generate stable booking reference
  const getBookingReference = () => {
    if (bookingReference) return bookingReference;
    return generateReservationCode();
  };

  // Generate stable payment ID
  const getPaymentId = () => {
    return paymentId || `PAY_${Date.now()}_${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
  };

  // Create display data with fallbacks
  const displayData = {
    flight: selectedFlight || selectedOutboundFlight || {
      airline: { name: 'British Airways' },
      flight: {
        number: 'BA 1407',
        departure: { airport: 'MAN', time: new Date().toISOString() },
        arrival: { airport: 'MLA', time: new Date().toISOString() }
      }
    },
    passengers: passengers.length > 0 ? passengers : [{ firstName: 'HUDIFA', lastName: 'MISRATI' }],
    email: email || '<EMAIL>',
    paymentId: getPaymentId(),
    bookingReference: getBookingReference(),
    totalPrice: totalPrice
  };

  useEffect(() => {
    // Check if we have booking data
    const hasData = finalNavigationData.bookingReference || paymentId || bookingReference || selectedFlight || selectedOutboundFlight;

    if (!hasData) {
      console.log('No booking data found, redirecting to home...');
      const timer = setTimeout(() => navigate('/'), 3000);
      return () => clearTimeout(timer);
    }

    console.log('Success page loaded with booking data');
  }, [finalNavigationData, paymentId, bookingReference, selectedFlight, selectedOutboundFlight, navigate]);

  // Handle document download
  const handleDownloadReservation = async () => {
    setIsDownloading(true);

    try {
      console.log('📥 Generating reservation document...');

      // Prepare booking data for backend
      const bookingData = {
        bookingReference: displayData.bookingReference,
        passengers: displayData.passengers,
        outboundFlight: null,
        returnFlight: null,
        totalPrice: parseFloat(displayData.totalPrice)
      };

      // Format flight data for backend
      const formatFlightForBackend = (flight) => {
        if (!flight) return null;

        // Extract stops information from flight data
        const stops = flight.flight?.stops || 0;
        const layovers = flight.flight?.layovers || [];

        // Format layover information for display
        const formatLayovers = (layovers) => {
          if (!layovers || layovers.length === 0) return null;
          return layovers.map(layover => ({
            airport: layover.airport || layover.id || 'LAY',
            city: layover.city || 'Layover City',
            duration: layover.duration || '1h 30m'
          }));
        };

        return {
          airline: flight.airline?.name || 'Unknown Airline',
          flightNumber: flight.flight?.number || flight.flightNumber || 'FL000',
          duration: flight.flight?.duration || flight.duration || '2h 30m',
          aircraft: flight.aircraft || 'Boeing 737-800',
          stops: stops,
          layovers: formatLayovers(layovers),
          departure: {
            code: flight.flight?.departure?.airport || flight.flight?.departure?.iataCode || 'DEP',
            city: flight.flight?.departure?.city || 'Departure City',
            time: flight.flight?.departure?.time || new Date().toISOString(),
            terminal: flight.flight?.departure?.terminal || '1'
          },
          arrival: {
            code: flight.flight?.arrival?.airport || flight.flight?.arrival?.iataCode || 'ARR',
            city: flight.flight?.arrival?.city || 'Arrival City',
            time: flight.flight?.arrival?.time || new Date().toISOString(),
            terminal: flight.flight?.arrival?.terminal || '1'
          }
        };
      };

      // Add flight data based on trip type
      if (tripType === 'return' && selectedOutboundFlight && selectedReturnFlight) {
        bookingData.outboundFlight = formatFlightForBackend(selectedOutboundFlight);
        bookingData.returnFlight = formatFlightForBackend(selectedReturnFlight);
      } else {
        const flight = selectedFlight || selectedOutboundFlight;
        bookingData.outboundFlight = formatFlightForBackend(flight);
      }

      // Call backend API to generate document
      const response = await fetch('/api/tickets/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success || !result.downloadUrl) {
        throw new Error(result.message || 'Failed to generate document');
      }

      // Download the document
      const downloadResponse = await fetch(result.downloadUrl);
      if (!downloadResponse.ok) {
        throw new Error('Failed to download document file');
      }

      const documentBlob = await downloadResponse.blob();

      // Create download link
      const url = window.URL.createObjectURL(documentBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${displayData.bookingReference}.pdf`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      window.URL.revokeObjectURL(url);

      // Show success toast briefly
      setToastMessage('Your booking confirmation has been downloaded.');
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);

    } catch (error) {
      console.error('❌ Document download failed:', error);
      setToastMessage('Failed to download document. Please try again.');
      setShowToast(true);
      setTimeout(() => setShowToast(false), 5000);
    } finally {
      setIsDownloading(false);
    }
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  return (
    <div className="min-h-screen aviation-gradient-hero relative overflow-hidden">
      {/* Premium Aviation Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-500/8 via-transparent to-accent-500/8"></div>
      <div className="absolute top-20 left-10 w-80 h-80 bg-brand-400/15 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-400/15 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-brand-300/10 to-accent-300/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

      {/* Subtle Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>

      <div className="relative z-10 min-h-screen flex items-center py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto w-full">
          {/* Premium Success Animation */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200, damping: 20 }}
            className="text-center mb-12"
          >
            {/* Premium Aviation Success Icon */}
            <div className="relative mb-12">
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
                className="w-40 h-40 bg-gradient-to-br from-green-400 via-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto shadow-aviation relative overflow-hidden"
              >
                {/* Premium Success checkmark */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5, type: "spring", stiffness: 400 }}
                  className="text-white"
                >
                  <svg className="w-20 h-20" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </motion.div>

                {/* Floating particles */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-white rounded-full opacity-60"
                    animate={{
                      y: [-20, -60, -20],
                      x: [0, Math.random() * 40 - 20, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.3,
                    }}
                    style={{
                      left: `${20 + i * 10}%`,
                      top: '50%',
                    }}
                  />
                ))}
              </motion.div>

              {/* Premium Aviation Glow effect */}
              <div className="absolute inset-0 w-40 h-40 bg-green-400 rounded-3xl mx-auto opacity-20 animate-pulse"></div>
            </div>

            {/* Premium Aviation Success Message */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mb-12"
            >
              <h1 className="text-6xl md:text-7xl font-black text-green-600 mb-6">
                🎉 Success!
              </h1>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-700 mb-8">
                Your Embassy-Approved
                <span className="bg-gradient-to-r from-accent-600 to-accent-500 bg-clip-text text-transparent block">
                  Flight Reservation is Ready!
                </span>
              </h2>

              <div className="max-w-4xl mx-auto">
                <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-8">
                  Congratulations! Your professional, embassy-approved flight reservation has been generated and is ready for your visa application.
                  <strong className="text-brand-800"> Download it now and get one step closer to your travel dreams!</strong>
                </p>
              </div>
            </motion.div>

            {/* Premium Aviation Trust Badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12"
            >
              <div className="bg-white/80 backdrop-blur-md rounded-2xl p-4 shadow-aviation border border-brand-200">
                <div className="flex items-center justify-center">
                  <svg className="w-6 h-6 mr-3 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="font-bold text-brand-700">Visa-Ready Format</span>
                </div>
              </div>
              <div className="bg-white/80 backdrop-blur-md rounded-2xl p-4 shadow-aviation border border-accent-200">
                <div className="flex items-center justify-center">
                  <svg className="w-6 h-6 mr-3 text-accent-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                  <span className="font-bold text-accent-700">Instant Download</span>
                </div>
              </div>
              <div className="bg-white/80 backdrop-blur-md rounded-2xl p-4 shadow-aviation border border-green-200">
                <div className="flex items-center justify-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clipRule="evenodd" />
                  </svg>
                  <span className="font-bold text-green-700">Secure & Verified</span>
                </div>
              </div>
            </motion.div>

            {/* Premium Download Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="text-center"
            >
              <button
                onClick={handleDownloadReservation}
                disabled={isDownloading}
                className="premium-button-large disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group mb-6"
              >
                {/* Button shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                {isDownloading ? (
                  <div className="flex items-center justify-center space-x-3 relative z-10">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    <span className="font-bold">Preparing Your Document...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-3 relative z-10">
                    <motion.div
                      animate={{ y: [0, -5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </motion.div>
                    <span className="font-bold">Download Your Reservation Now</span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                )}
              </button>

              {/* Additional Actions */}
              <div className="flex flex-wrap justify-center gap-4 text-sm">
                <div className="flex items-center text-accent-600">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  Email copy sent to {displayData.email}
                </div>
                <div className="flex items-center text-neutral-500">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                  Valid for 30 days
                </div>
              </div>
            </motion.div>
        </motion.div>

          {/* Premium Order Details */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
            className="premium-card relative overflow-hidden"
          >
            {/* Premium background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>

            {/* Header */}
            <div className="relative z-10 bg-gradient-to-r from-brand-50 to-accent-50 px-8 py-6 border-b border-neutral-200">
              <h3 className="text-3xl font-bold text-neutral-900 flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full flex items-center justify-center mr-4 shadow-glow">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                Your Reservation Details
              </h3>
              <p className="text-neutral-600 mt-2 text-lg">
                Everything you need for your visa application is ready below
              </p>
            </div>

            {/* Premium Content */}
            <div className="relative z-10 p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Premium Payment Information */}
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-neutral-50 to-white rounded-2xl p-6 border border-neutral-200 shadow-soft hover:shadow-medium transition-all duration-300 group">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-neutral-400 to-neutral-500 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm font-bold text-neutral-700 uppercase tracking-wide">Payment ID</span>
                      </div>
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-accent-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="font-mono text-lg font-bold text-neutral-900 group-hover:text-brand-600 transition-colors">{displayData.paymentId}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-brand-50 to-brand-100 rounded-2xl p-6 border-2 border-brand-200 shadow-glow hover:shadow-luxury transition-all duration-300 group">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-brand-500 to-brand-600 rounded-full flex items-center justify-center mr-3 shadow-glow">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm font-bold text-brand-700 uppercase tracking-wide">Booking Reference</span>
                      </div>
                      <span className="font-mono text-2xl font-bold text-brand-900 group-hover:scale-105 transition-transform">{displayData.bookingReference}</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-accent-50 to-accent-100 rounded-2xl p-6 border-2 border-accent-200 shadow-success-glow hover:shadow-luxury transition-all duration-300 group">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center mr-3 shadow-success-glow">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm font-bold text-accent-700 uppercase tracking-wide">Amount Paid</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="price-final group-hover:scale-105 transition-transform">${displayData.totalPrice}</span>
                        <div className="text-xs text-accent-600 font-medium">
                          <div>✓ Paid</div>
                          <div>✓ Confirmed</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Premium Contact & Date Information */}
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-neutral-50 to-white rounded-2xl p-6 border border-neutral-200 shadow-soft hover:shadow-medium transition-all duration-300 group">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                          </svg>
                        </div>
                        <span className="text-sm font-bold text-neutral-700 uppercase tracking-wide">Email Address</span>
                      </div>
                      <span className="font-mono text-lg font-bold text-neutral-900 break-all group-hover:text-purple-600 transition-colors">{displayData.email}</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-neutral-50 to-white rounded-2xl p-6 border border-neutral-200 shadow-soft hover:shadow-medium transition-all duration-300 group">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm font-bold text-neutral-700 uppercase tracking-wide">Booking Date</span>
                      </div>
                      <span className="font-bold text-lg text-neutral-900 group-hover:text-blue-600 transition-colors">
                        {new Date().toLocaleDateString('en-GB', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-accent-50 to-accent-100 rounded-2xl p-6 border-2 border-accent-200 shadow-success-glow hover:shadow-luxury transition-all duration-300 group relative overflow-hidden">
                    {/* Premium background effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-accent-400/10 to-accent-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <div className="relative z-10 flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center mr-4 shadow-success-glow group-hover:scale-110 transition-transform">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-xl font-bold text-accent-700 mb-1">Status: Confirmed ✓</p>
                        <p className="text-accent-600 font-medium">Your reservation has been successfully processed and is ready for embassy submission</p>
                      </div>
                    </div>

                    {/* Premium shine effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                  </div>

                  {/* Next Steps */}
                  <div className="bg-gradient-to-r from-brand-50 to-purple-50 rounded-2xl p-6 border border-brand-200">
                    <h4 className="text-lg font-bold text-neutral-800 mb-4 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-brand-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      Next Steps for Your Visa Application
                    </h4>
                    <ul className="space-y-2 text-neutral-700">
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-accent-500 rounded-full mr-3"></span>
                        Download your reservation document above
                      </li>
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-accent-500 rounded-full mr-3"></span>
                        Include it with your visa application documents
                      </li>
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-accent-500 rounded-full mr-3"></span>
                        Submit to embassy with confidence!
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Premium shine effect for the whole card */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1500"></div>
          </motion.div>

          {/* Premium Success Actions */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
            className="text-center mt-12"
          >
            <div className="inline-flex flex-col items-center bg-white/80 backdrop-blur-sm rounded-3xl px-12 py-8 shadow-soft border border-neutral-200 relative overflow-hidden group">
              {/* Background effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-brand-500/10 to-accent-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div className="relative z-10">
                <h3 className="text-2xl font-bold text-neutral-900 mb-4">
                  🎉 Congratulations! Your Journey Begins Now
                </h3>
                <p className="text-neutral-600 mb-6 max-w-md">
                  You're one step closer to your travel dreams. Need another reservation or have questions?
                </p>

                <div className="flex flex-wrap justify-center gap-4">
                  <button
                    onClick={() => window.location.href = '/'}
                    className="premium-button inline-flex items-center group"
                  >
                    <svg className="w-5 h-5 mr-2 group-hover:animate-bounce-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                    Book Another Flight
                  </button>

                  <a href="mailto:<EMAIL>" className="btn-secondary inline-flex items-center group">
                    <svg className="w-5 h-5 mr-2 group-hover:animate-bounce-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Contact Support
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Toast Notification */}
      <ToastNotification
        message={toastMessage}
        show={showToast}
        onClose={() => setShowToast(false)}
      />
    </div>
  );
};

export default SuccessPage;
