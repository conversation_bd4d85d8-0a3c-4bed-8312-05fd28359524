import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { getCTANavigationProps } from '../utils/ctaNavigation';

const UseCasesPage = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Set page title and meta description for SEO
  useEffect(() => {
    document.title = 'Use Cases | VerifiedOnward - Embassy-Approved Flight Reservations';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Discover when you need VerifiedOnward flight reservations: visa applications, digital nomad visas, flexible travel plans, and border entry requirements. Trusted by 75,000+ travelers worldwide.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservations';
    };
  }, []);
  const useCases = [
    {
      title: "Visa Applications",
      subtitle: "Schengen, US, UK & More",
      description: "Need proof of onward travel for your visa application? You don't need to purchase an actual ticket upfront. Our professionally formatted flight reservations are specifically designed for visa applications and meet common documentation standards worldwide. Get the documentation you need without the financial commitment.",
      icon: "✈️",
      features: [
        "Professionally formatted documentation",
        "Meets standards across 195+ countries",
        "Instant document delivery",
        "Valid for 48 hours"
      ],
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      iconColor: "text-blue-600"
    },
    {
      title: "Digital Nomad Visa",
      subtitle: "Remote Work & Long-term Stay",
      description: "Planning to work remotely from abroad? Digital nomad visas often require proof of onward travel to demonstrate you won't overstay. Our flight reservations provide the perfect solution - showing your travel intent without locking you into rigid dates. Freedom to work from anywhere, responsibly.",
      icon: "💻",
      features: [
        "Perfect for remote workers",
        "Flexible travel dates",
        "Works for multiple countries",
        "No commitment to actual travel"
      ],
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      iconColor: "text-green-600"
    },
    {
      title: "Flexible Travel Plans",
      subtitle: "Spontaneous & Open-ended Trips",
      description: "Love spontaneous travel but need to show onward journey proof? Our flight reservations give you the documentation you need while preserving your freedom to explore. No rigid schedules, no expensive ticket changes - just the flexibility to travel on your terms while meeting entry requirements.",
      icon: "🎒",
      features: [
        "Change plans without penalty",
        "No rigid schedules required",
        "Perfect for backpackers",
        "Explore at your own pace"
      ],
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      iconColor: "text-purple-600"
    },
    {
      title: "Border Entry Requirements",
      subtitle: "Immigration & Customs Control",
      description: "Entering countries with strict immigration policies? Border officials often require proof of onward travel to ensure you won't overstay. Our professional flight reservations provide the credible documentation you need for smooth entry, preventing delays and complications at immigration checkpoints.",
      icon: "🛂",
      features: [
        "Meets immigration standards worldwide",
        "Prevents border complications",
        "Professional documentation",
        "Instant verification"
      ],
      bgColor: "bg-accent-50",
      borderColor: "border-accent-200",
      iconColor: "text-accent-600"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-blue-500/5 to-accent-500/5"></div>
        
        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              USE CASES
            </div>
            
            <h1 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
              When You Need
              <br />
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">Flight Reservations</span>
            </h1>

            <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-8">
              From visa applications to flexible travel plans, discover how VerifiedOnward
              <br className="hidden md:block" />
              helps travelers in various situations worldwide.
            </p>

            <div className="max-w-4xl mx-auto mb-12">
              <p className="text-lg text-brand-600 leading-relaxed">
                Whether you're applying for a Schengen visa, planning a digital nomad adventure, or need proof of onward travel for immigration,
                our professionally formatted flight reservations provide the documentation you need. Trusted by over 75,000 travelers
                across 195+ countries, we make travel planning simpler and more flexible without the financial commitment of purchasing actual tickets upfront.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Use Cases Grid */}
      <section className="py-20">
        <div className="container-modern">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-brand-800 mb-4">
              Popular Use Cases
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-brand-500 to-accent-500 mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`${useCase.bgColor} rounded-3xl p-8 border-2 ${useCase.borderColor} shadow-aviation hover:shadow-aviation-hover transition-all duration-300 group relative overflow-hidden transform hover:-translate-y-2 h-full flex flex-col`}
              >
                {/* Premium shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                <div className="relative z-10 flex flex-col h-full">
                  <div className="flex items-start space-x-6 mb-6">
                    <div className="w-20 h-20 rounded-2xl bg-white/80 backdrop-blur-sm shadow-aviation flex items-center justify-center text-4xl">
                      {useCase.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-neutral-800 mb-2">{useCase.title}</h3>
                      <p className={`text-sm font-semibold ${useCase.iconColor} mb-4`}>{useCase.subtitle}</p>
                    </div>
                  </div>

                  <p className="text-neutral-700 leading-relaxed mb-6 flex-grow">{useCase.description}</p>

                  <div className="space-y-3 mt-auto">
                    {useCase.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <div className={`w-5 h-5 ${useCase.iconColor} rounded-full flex items-center justify-center bg-white/60`}>
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-neutral-700 font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-brand-500 via-brand-600 to-accent-600 relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/20 to-accent-500/20"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-white/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-accent-400/20 to-transparent rounded-full blur-2xl"></div>

        <div className="container-modern text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Trust Badge */}
            <div className="inline-flex items-center bg-green-100 text-green-800 px-6 py-3 rounded-full text-sm font-bold mb-8">
              <span className="w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse"></span>
              99.7% Visa Success Rate
            </div>

            <h2 className="text-4xl md:text-5xl font-black text-white mb-6">
              Ready to Get Started?
            </h2>

            <p className="text-lg text-brand-100 mb-4 max-w-2xl mx-auto font-medium">
              Secure your visa-ready ticket in minutes.
            </p>

            <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto">
              Join 75,000+ travelers who trust VerifiedOnward for their flight reservations.
            </p>

            <Link
              className="inline-flex items-center bg-white text-brand-600 px-12 py-5 rounded-2xl text-lg font-bold hover:bg-brand-50 transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 group"
              {...getCTANavigationProps(navigate, location.pathname)}
            >
              <span>Start Your Reservation</span>
              <svg className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>

            {/* Additional trust indicators */}
            <div className="mt-8 flex flex-wrap justify-center gap-6 text-brand-100 text-sm">
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Instant Download
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                Secure Payment
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Email Support
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default UseCasesPage;
