import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  CreditCardIcon,
  LockClosedIcon,
  ShieldCheckIcon,
  ClockIcon,
  DocumentCheckIcon,
  ArrowLeftIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';

const CheckoutSimpleFixed = () => {
  const [bookingData, setBookingData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.log('🔍 CheckoutSimpleFixed: Initializing...');
    
    // Create test data directly
    const urlParams = new URLSearchParams(location.search);
    const isTestMode = urlParams.get('test') === 'true';
    const tripType = urlParams.get('tripType') || 'oneWay';
    
    if (isTestMode) {
      const testData = {
        selectedFlight: {
          id: 'test-flight-123',
          airline: 'Emirates',
          flightNumber: 'EK205',
          departure: {
            airport: 'JFK',
            city: 'New York',
            time: '10:00',
            date: '2025-02-15'
          },
          arrival: {
            airport: 'LHR',
            city: 'London',
            time: '22:00',
            date: '2025-02-15'
          },
          price: tripType === 'return' ? 9.98 : 4.99,
          duration: '8h 00m'
        },
        passengers: [
          { id: 1, firstName: 'John', lastName: 'Smith' }
        ],
        email: '<EMAIL>',
        tripType: tripType
      };
      
      console.log('✅ Setting test data:', testData);
      setBookingData(testData);
    } else {
      // Fallback data
      const fallbackData = {
        selectedFlight: {
          id: 'fallback-flight',
          airline: 'KLM Royal Dutch Airlines',
          flightNumber: 'KL644',
          departure: {
            airport: 'JFK',
            city: 'New York',
            time: '10:00',
            date: '2025-02-15'
          },
          arrival: {
            airport: 'AMS',
            city: 'Amsterdam',
            time: '22:00',
            date: '2025-02-15'
          },
          price: 4.99,
          duration: '8h 00m'
        },
        passengers: [
          { id: 1, firstName: 'Sample', lastName: 'Passenger' }
        ],
        email: '<EMAIL>',
        tripType: 'oneWay'
      };
      
      console.log('✅ Setting fallback data:', fallbackData);
      setBookingData(fallbackData);
    }
    
    setIsLoading(false);
  }, [location.search]);

  const calculateTotalPrice = () => {
    if (!bookingData) return 4.99;
    const tripType = bookingData.tripType || 'oneWay';
    return tripType === 'return' || tripType === 'roundTrip' ? 9.98 : 4.99;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-brand-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-brand-200 rounded-full animate-spin mx-auto mb-4"></div>
          <h3 className="text-xl font-bold text-brand-900">Loading Checkout</h3>
        </div>
      </div>
    );
  }

  console.log('🔍 Rendering with bookingData:', bookingData);

  return (
    <div className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-brand-50">
      {/* Header */}
      <div className="bg-white border-b border-brand-100 shadow-soft sticky top-0 z-50">
        <div className="container-modern py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-brand-600 hover:text-brand-700 transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5" />
              <span className="font-medium">Back</span>
            </button>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-semibold text-brand-700">SSL Secured Checkout</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container-modern py-8">
        <div className="max-w-6xl mx-auto">
          {/* Page Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-brand-900 mb-4">
              Complete Your Flight Reservation
            </h1>
            <p className="text-lg text-brand-600 max-w-2xl mx-auto">
              Secure, embassy-approved flight reservations delivered instantly
            </p>

            {/* Debug Info */}
            <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded-lg text-sm max-w-4xl mx-auto">
              <strong>Debug Info:</strong> 
              bookingData: {bookingData ? '✅ EXISTS' : '❌ NULL'} | 
              selectedFlight: {bookingData?.selectedFlight ? '✅ EXISTS' : '❌ NULL'}
              {bookingData?.selectedFlight && (
                <div className="mt-1">
                  Flight: {bookingData.selectedFlight.airline} {bookingData.selectedFlight.flightNumber} 
                  ({bookingData.selectedFlight.departure?.airport} → {bookingData.selectedFlight.arrival?.airport})
                </div>
              )}
            </div>
          </div>

          {/* Two-Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Booking Details */}
            <div className="lg:col-span-2">
              {/* Booking Overview Card */}
              <div className="bg-white rounded-xl p-6 border border-brand-100 shadow-soft mb-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-brand-500 to-brand-600 rounded-xl flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-brand-900">Booking Overview</h3>
                    <p className="text-brand-600">Complete reservation summary</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Passenger Details */}
                  <div className="bg-white rounded-xl p-6 border border-brand-100 shadow-soft">
                    <div className="flex items-center mb-4">
                      <svg className="w-5 h-5 text-brand-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                      <h4 className="font-bold text-brand-900">Passenger Details</h4>
                    </div>
                    {bookingData?.passengers && bookingData.passengers.length > 0 ? (
                      <div className="space-y-2">
                        {bookingData.passengers.map((passenger, index) => (
                          <div key={passenger.id || index} className="flex justify-between items-center py-2 border-b border-brand-100 last:border-b-0">
                            <span className="text-brand-600 text-sm">Passenger {index + 1}</span>
                            <span className="font-semibold text-brand-900">{passenger.firstName} {passenger.lastName}</span>
                          </div>
                        ))}
                        <div className="flex justify-between items-center py-2">
                          <span className="text-brand-600 text-sm">Email</span>
                          <span className="font-semibold text-brand-900">{bookingData.email}</span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <div className="text-brand-500">No passenger details available</div>
                      </div>
                    )}
                  </div>

                  {/* Flight Details */}
                  <div className="bg-white rounded-xl p-6 border border-brand-100 shadow-soft">
                    <div className="flex items-center mb-4">
                      <GlobeAltIcon className="w-5 h-5 text-brand-600 mr-2" />
                      <h4 className="font-bold text-brand-900">Flight Details</h4>
                    </div>
                    {bookingData && bookingData.selectedFlight ? (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center py-2 border-b border-brand-100">
                          <span className="text-brand-600 text-sm">Airline</span>
                          <span className="font-semibold text-brand-900">{bookingData.selectedFlight.airline}</span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-brand-100">
                          <span className="text-brand-600 text-sm">Flight</span>
                          <span className="font-semibold text-brand-900">{bookingData.selectedFlight.flightNumber}</span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-brand-100">
                          <span className="text-brand-600 text-sm">Route</span>
                          <span className="font-semibold text-brand-900">
                            {bookingData.selectedFlight.departure?.airport} → {bookingData.selectedFlight.arrival?.airport}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-brand-100">
                          <span className="text-brand-600 text-sm">Date</span>
                          <span className="font-semibold text-brand-900">{bookingData.selectedFlight.departure?.date}</span>
                        </div>
                        <div className="flex justify-between items-center py-2">
                          <span className="text-brand-600 text-sm">Time</span>
                          <span className="font-semibold text-brand-900">
                            {bookingData.selectedFlight.departure?.time} - {bookingData.selectedFlight.arrival?.time}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <div className="text-brand-500">No flight selected</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Payment */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl p-6 border border-brand-100 shadow-soft sticky top-24">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                    <LockClosedIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-brand-900">Secure Payment</h3>
                    <p className="text-brand-600">SSL encrypted</p>
                  </div>
                </div>

                {/* Price Display */}
                <div className="text-center mb-8 p-6 bg-gradient-to-r from-brand-50 to-brand-100/50 rounded-xl border border-brand-200">
                  <div className="text-4xl font-bold text-brand-900 mb-2">${calculateTotalPrice()}</div>
                  <div className="text-brand-600 font-medium">Embassy-Approved Flight Reservation</div>
                </div>

                {/* Payment Button */}
                <button className="w-full bg-gradient-to-r from-brand-500 to-brand-600 text-white font-bold py-4 px-6 rounded-xl hover:from-brand-600 hover:to-brand-700 transition-all duration-300 shadow-aviation mb-4">
                  <div className="flex items-center justify-center">
                    <CreditCardIcon className="h-5 w-5 mr-3" />
                    Pay ${calculateTotalPrice()} with Card
                  </div>
                </button>

                {/* Security Footer */}
                <div className="text-center pt-6 border-t border-brand-100">
                  <div className="flex items-center justify-center text-sm text-brand-600 mb-2">
                    <LockClosedIcon className="h-4 w-4 mr-2" />
                    256-bit SSL encryption
                  </div>
                  <p className="text-xs text-brand-500">
                    Your payment information is secure and encrypted
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSimpleFixed;
