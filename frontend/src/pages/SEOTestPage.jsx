import React, { useState, useEffect } from 'react';
import { generateSEOReport, validateAllBlogPosts } from '../utils/seoValidator';

const SEOTestPage = () => {
  const [report, setReport] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runReport = async () => {
      try {
        const validation = validateAllBlogPosts();
        setReport(validation);
        
        // Also log to console
        generateSEOReport();
      } catch (error) {
        console.error('Error running SEO report:', error);
      } finally {
        setLoading(false);
      }
    };

    runReport();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-600 mx-auto mb-4"></div>
          <p className="text-neutral-600">Running SEO Analysis...</p>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-neutral-600">Failed to generate SEO report</p>
        </div>
      </div>
    );
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    if (score >= 70) return 'text-orange-600';
    return 'text-red-600';
  };

  const getGradeColor = (grade) => {
    if (grade === 'A') return 'bg-green-100 text-green-800';
    if (grade === 'B') return 'bg-yellow-100 text-yellow-800';
    if (grade === 'C') return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="min-h-screen bg-neutral-50 py-12">
      <div className="max-w-6xl mx-auto px-6">
        <header className="mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 mb-4">
            VerifiedOnward Blog SEO Report
          </h1>
          <p className="text-xl text-neutral-700">
            Comprehensive analysis of all blog posts for Hybrid SEO optimization
          </p>
        </header>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200">
            <h3 className="text-sm font-medium text-neutral-600 mb-2">Total Posts</h3>
            <p className="text-3xl font-bold text-neutral-900">{report.summary.totalPosts}</p>
          </div>
          
          <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200">
            <h3 className="text-sm font-medium text-neutral-600 mb-2">Average Score</h3>
            <p className={`text-3xl font-bold ${getScoreColor(report.summary.averageScore)}`}>
              {report.summary.averageScore}/100
            </p>
          </div>
          
          <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200">
            <h3 className="text-sm font-medium text-neutral-600 mb-2">Perfect Posts</h3>
            <p className="text-3xl font-bold text-green-600">{report.summary.perfectPosts}</p>
          </div>
          
          <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200">
            <h3 className="text-sm font-medium text-neutral-600 mb-2">Total Issues</h3>
            <p className="text-3xl font-bold text-red-600">{report.summary.totalIssues}</p>
          </div>
        </div>

        {/* Grade Distribution */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 mb-12">
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">Grade Distribution</h3>
          <div className="flex flex-wrap gap-4">
            {Object.entries(report.summary.gradeDistribution).map(([grade, count]) => (
              <div key={grade} className="flex items-center space-x-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getGradeColor(grade)}`}>
                  Grade {grade}
                </span>
                <span className="text-neutral-600">{count} posts</span>
              </div>
            ))}
          </div>
        </div>

        {/* Individual Post Results */}
        <div className="bg-white rounded-xl shadow-sm border border-neutral-200">
          <div className="p-6 border-b border-neutral-200">
            <h3 className="text-lg font-semibold text-neutral-900">Individual Post Analysis</h3>
          </div>
          
          <div className="divide-y divide-neutral-200">
            {report.results.map((post, index) => (
              <div key={index} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-medium text-neutral-900 mb-1">
                      {post.title}
                    </h4>
                    <p className="text-sm text-neutral-600">/{post.slug}</p>
                  </div>
                  
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${getScoreColor(post.validation.score)}`}>
                      {post.validation.score}/100
                    </div>
                    <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${getGradeColor(post.validation.grade)}`}>
                      Grade {post.validation.grade}
                    </span>
                  </div>
                </div>

                {/* Issues */}
                {post.validation.issues.length > 0 && (
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-red-600 mb-2">Issues:</h5>
                    <ul className="space-y-1">
                      {post.validation.issues.map((issue, i) => (
                        <li key={i} className="text-sm text-red-600 flex items-center">
                          <span className="mr-2">❌</span>
                          {issue}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Warnings */}
                {post.validation.warnings.length > 0 && (
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-yellow-600 mb-2">Warnings:</h5>
                    <ul className="space-y-1">
                      {post.validation.warnings.map((warning, i) => (
                        <li key={i} className="text-sm text-yellow-600 flex items-center">
                          <span className="mr-2">⚠️</span>
                          {warning}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Successes */}
                {post.validation.successes.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-green-600 mb-2">Successes:</h5>
                    <ul className="space-y-1">
                      {post.validation.successes.slice(0, 3).map((success, i) => (
                        <li key={i} className="text-sm text-green-600 flex items-center">
                          <span className="mr-2">✅</span>
                          {success}
                        </li>
                      ))}
                      {post.validation.successes.length > 3 && (
                        <li className="text-sm text-neutral-500">
                          +{post.validation.successes.length - 3} more successes
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Action Items */}
        <div className="mt-12 bg-brand-50 rounded-xl p-6 border border-brand-200">
          <h3 className="text-lg font-semibold text-brand-900 mb-4">Next Steps</h3>
          <ul className="space-y-2 text-brand-800">
            <li>• Fix all critical issues (❌) to improve scores</li>
            <li>• Address warnings (⚠️) for optimal SEO performance</li>
            <li>• Ensure all posts have FAQ sections for rich results</li>
            <li>• Verify markdown formatting is clean and consistent</li>
            <li>• Test Google rich results detection after fixes</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SEOTestPage;
