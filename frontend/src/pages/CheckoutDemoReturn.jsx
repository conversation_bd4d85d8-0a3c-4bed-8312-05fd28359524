import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '../context/BookingContext';

const CheckoutDemoReturn = () => {
  const navigate = useNavigate();
  const {
    setSelectedFlight,
    setSelectedOutboundFlight,
    setSelectedReturnFlight,
    setPassengers,
    setEmail,
    setTripType
  } = useBooking();

  useEffect(() => {
    // Set up demo return flight data
    const demoOutboundFlight = {
      id: 'demo-outbound-flight',
      airline: {
        name: 'Emirates',
        code: 'EK',
        logo: 'https://via.placeholder.com/40x40?text=EK'
      },
      flightNumber: 'EK 15',
      from: 'London Heathrow (LHR)',
      to: 'Dubai International (DXB)',
      departure: {
        airport: 'London Heathrow (LHR)',
        time: '10:30 AM'
      },
      arrival: {
        airport: 'Dubai International (DXB)',
        time: '8:45 PM'
      },
      duration: '7h 15m',
      price: '4.99'
    };

    const demoReturnFlight = {
      id: 'demo-return-flight',
      airline: {
        name: 'Emirates',
        code: 'EK',
        logo: 'https://via.placeholder.com/40x40?text=EK'
      },
      flightNumber: 'EK 16',
      from: 'Dubai International (DXB)',
      to: 'London Heathrow (LHR)',
      departure: {
        airport: 'Dubai International (DXB)',
        time: '2:15 AM'
      },
      arrival: {
        airport: 'London Heathrow (LHR)',
        time: '6:30 AM'
      },
      duration: '7h 15m',
      price: '4.99'
    };

    const demoPassengers = [
      { id: 1, firstName: 'Michael', lastName: 'Smith' }
    ];

    const demoEmail = '<EMAIL>';

    console.log('🔄 CheckoutDemoReturn: Setting demo return flight data');
    console.log('🔄 CheckoutDemoReturn: Expected total = $9.98');

    // Set the demo data in context using individual setters
    setSelectedFlight(demoOutboundFlight); // For one-way compatibility
    setSelectedOutboundFlight(demoOutboundFlight);
    setSelectedReturnFlight(demoReturnFlight);
    setPassengers(demoPassengers);
    setEmail(demoEmail);
    setTripType('return'); // This is key for return pricing

    // Navigate to checkout after a short delay
    setTimeout(() => {
      navigate('/checkout');
    }, 500);
  }, [navigate, setSelectedFlight, setSelectedOutboundFlight, setSelectedReturnFlight, setPassengers, setEmail, setTripType]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Setting up return flight demo...</p>
      </div>
    </div>
  );
};

export default CheckoutDemoReturn;
