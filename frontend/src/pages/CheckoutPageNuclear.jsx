import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast, ToastContainer } from 'react-toastify';
import {
  CreditCardIcon,
  ArrowLeftIcon,
  ShieldCheckIcon,
  UserIcon,
  PaperAirplaneIcon,
  ExclamationTriangleIcon,
  PencilSquareIcon,
  CheckCircleIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import { getCheckoutData } from '../utils/sessionStorageHelper';
import { useBooking } from '../context/BookingContext';
import { paymentAPI } from '../services/api';
import { generateReservationCode } from '../utils/reservationCodeGenerator';

const BookingSummaryPage = () => {
  console.log('✅ BookingSummaryPage: Professional checkout page loading...');
  console.log('🔧 DEBUGGING: CheckoutPageNuclear with FIXED pricing and edit functionality loaded!');

  // IMMEDIATE DEBUG: Test if component is actually rendering
  console.log('🚨 IMMEDIATE DEBUG: Component function called at', new Date().toISOString());

  // TEMPORARY: Early return to test if component is being called
  if (process.env.NODE_ENV === 'development') {
    console.log('🚨 EARLY RETURN TEST: Component is being called!');
    // Enable early return to test component loading
    return <div className="min-h-screen bg-red-500 flex items-center justify-center"><h1 className="text-white text-4xl">COMPONENT IS WORKING!</h1></div>;
  }

  console.log('🔍 DEBUG: About to initialize hooks...');

  const navigate = useNavigate();
  const location = useLocation();

  console.log('🔍 DEBUG: Navigation hooks initialized');

  let bookingHookData;
  try {
    console.log('🔍 DEBUG: About to call useBooking...');
    bookingHookData = useBooking();
    console.log('🔍 DEBUG: useBooking successful:', bookingHookData);
  } catch (err) {
    console.error('❌ DEBUG: useBooking failed:', err);
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          <h1 className="text-xl font-bold text-red-600 mb-4">useBooking Error</h1>
          <p className="text-gray-600">{err.message}</p>
        </div>
      </div>
    );
  }

  const {
    selectedFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers,
    email,
    tripType,
    updateBookingData
  } = bookingHookData;

  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingStripe, setIsProcessingStripe] = useState(false);
  const [isProcessingPayPal, setIsProcessingPayPal] = useState(false);
  const [bookingData, setBookingData] = useState(null);

  // Derived state for display
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [cardholderName, setCardholderName] = useState('');
  const [billingAddress, setBillingAddress] = useState({
    street: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US'
  });

  // Load booking data from multiple sources
  useEffect(() => {
    console.log('✅ BookingSummaryPage: Loading booking data...');
    console.log('✅ BookingSummaryPage: Context data:', { selectedFlight, selectedOutboundFlight, selectedReturnFlight, passengers, email, tripType });
    console.log('✅ BookingSummaryPage: location.state:', location.state);
    console.log('✅ BookingSummaryPage: sessionStorage data:', getCheckoutData());

    try {
      let data = null;

      // Priority 1: Use React Context data if available (REAL FLIGHT DATA)
      if ((selectedFlight || selectedOutboundFlight) && passengers.length > 0 && email) {
        console.log('✅ BookingSummaryPage: Using REAL context data from flight selection');
        data = {
          selectedFlight,
          selectedOutboundFlight,
          selectedReturnFlight,
          passengers,
          email,
          tripType
        };
      }
      // Priority 2: Use location.state data (from SearchResultsPage navigation)
      else if (location.state && location.state.bookingData) {
        console.log('✅ BookingSummaryPage: Using location.state data from SearchResultsPage');
        data = location.state.bookingData;
      }
      // Priority 3: Use sessionStorage data (from page refresh or direct navigation)
      else {
        const sessionData = getCheckoutData();
        if (sessionData && (sessionData.selectedFlight || sessionData.selectedOutboundFlight)) {
          console.log('✅ BookingSummaryPage: Using sessionStorage data');
          data = sessionData;
        }
        // Priority 4: Check for test mode in development
        else {
        const urlParams = new URLSearchParams(window.location.search);
        const hasTestMode = urlParams.get('test') === 'true' || process.env.NODE_ENV === 'development';

        if (hasTestMode) {
          console.log('🧪 BookingSummaryPage: Creating test data for development');
          data = {
            tripType: 'oneWay',
            passengers: [{ firstName: 'Test', lastName: 'User' }],
            email: '<EMAIL>',
            flight: {
              id: 'test-flight-1',
              airline: { name: 'Test Airlines', code: 'TA' },
              flightNumber: 'TA 123',
              flight: {
                departure: { time: '2024-01-01T10:00:00Z', airport: 'JFK' },
                arrival: { time: '2024-01-01T14:00:00Z', airport: 'LAX' }
              },
              price: { total: 4.99 }
            }
          };
        } else {
          console.log('❌ BookingSummaryPage: No booking data found - redirecting to search');
          setError('No booking data found. Please start a new flight search.');
          setTimeout(() => {
            navigate('/');
          }, 2000);
          return;
        }
        }
      }

      if (data) {
        console.log('✅ BookingSummaryPage: Setting booking data:', data);
        setBookingData(data);
        setError(null);
      }

    } catch (err) {
      console.error('❌ BookingSummaryPage: Error loading booking data:', err);
      setError('Error loading booking data. Please try again.');
    } finally {
      // Add a small delay to show loading state
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 500);
    }

    return () => clearTimeout(timer);
  }, [location.state, selectedFlight, selectedOutboundFlight, selectedReturnFlight, passengers, email, tripType, navigate]);

  // Calculate total price based on trip type (NOT passenger count)
  const calculateTotalPrice = () => {
    if (!bookingData) return '4.99';
    const tripType = bookingData.tripType || 'oneWay';

    // Price is per FLIGHT, not per passenger
    // One-way: $4.99, Return: $4.99 + $4.99 = $9.98
    if (tripType === 'return') {
      return '9.98'; // Outbound + Return flight
    } else {
      return '4.99'; // One-way flight only
    }
  };

  // Handle edit details with preserved state - Navigate directly to passenger details
  const handleEditDetails = () => {
    try {
      // Save current checkout progress to sessionStorage
      const checkoutProgress = {
        step: 'payment',
        timestamp: Date.now(),
        returnUrl: '/checkout',
        bookingData: bookingData,
        totalPrice: calculateTotalPrice()
      };

      sessionStorage.setItem('checkoutProgress', JSON.stringify(checkoutProgress));
      console.log('✅ CheckoutPage: Progress saved for edit flow');

      // Navigate back to homepage with edit mode state and direct passenger details access
      navigate('/', {
        state: {
          editMode: true,
          showPassengerDetailsDirectly: true, // New flag to show passenger details immediately
          preservedData: {
            // Flight search data
            from: bookingData.departureAirport || bookingData.from,
            to: bookingData.arrivalAirport || bookingData.to,
            departureDate: bookingData.departureDate,
            returnDate: bookingData.returnDate,
            tripType: bookingData.tripType,

            // Passenger data
            passengers: bookingData.passengers,
            email: bookingData.email,

            // Flight selections
            selectedFlight: bookingData.selectedFlight,
            selectedOutboundFlight: bookingData.selectedOutboundFlight,
            selectedReturnFlight: bookingData.selectedReturnFlight
          },
          returnUrl: '/checkout'
        }
      });

      toast.info('Redirecting to edit passenger details...', {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

    } catch (err) {
      console.error('❌ CheckoutPage: Error in handleEditDetails:', err);
      toast.error('Error navigating to edit details. Please try again.');
    }
  };

  // Get departure flight data from various sources
  const getDepartureFlightData = () => {
    if (!bookingData) return null;

    console.log('🔍 getDepartureFlightData: Checking bookingData:', bookingData);

    // Handle different data formats from various sources
    const departureData = bookingData.selectedFlight ||           // From BookingContext
                         bookingData.selectedOutboundFlight ||    // From BookingContext (return trips)
                         bookingData.flight ||                    // From SearchResultsPage navigation
                         bookingData.outboundFlight;              // From SearchResultsPage navigation

    console.log('🔍 getDepartureFlightData: Found departure flight data:', departureData);
    return departureData;
  };

  const getReturnFlightData = () => {
    if (!bookingData) return null;

    console.log('🔍 getReturnFlightData: Checking bookingData:', bookingData);

    // Handle different data formats from various sources
    const returnData = bookingData.selectedReturnFlight ||     // From BookingContext
                      bookingData.returnFlight;                // From SearchResultsPage navigation

    console.log('🔍 getReturnFlightData: Found return flight data:', returnData);
    return returnData;
  };

  // Handle Stripe payment
  const handleStripePayment = async () => {
    // Prevent double-clicking and ensure mutual exclusivity
    if (isProcessingStripe || isProcessingPayPal) return;

    setIsProcessingStripe(true);
    setError(null);

    try {
      console.log('💳 Processing Stripe payment...');

      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate booking reference
      const bookingRef = generateReservationCode();

      // Show success toast (fixed: removed duplicate emoji)
      toast.success('Payment successful! Your verified flight reservation is ready to download.');

      // Navigate to success page
      setTimeout(() => {
        navigate('/success', {
          state: {
            bookingReference: bookingRef,
            paymentData: { success: true, paymentId: `stripe_${Date.now()}`, method: 'stripe' },
            selectedFlight: getDepartureFlightData(),
            returnFlight: getReturnFlightData(),
            passengers: bookingData?.passengers || [],
            email: bookingData?.email || '',
            tripType: bookingData?.tripType || 'oneWay',
            totalPrice: calculateTotalPrice()
          }
        });
      }, 1500);

    } catch (err) {
      console.error('❌ Stripe payment error:', err);
      setError('Payment failed. Please try again.');
      setIsProcessingStripe(false);
    }
  };

  // Handle PayPal payment
  const handlePayPalPayment = async () => {
    // Prevent double-clicking and ensure mutual exclusivity
    if (isProcessingStripe || isProcessingPayPal) return;

    setIsProcessingPayPal(true);
    setError(null);

    try {
      console.log('💰 Processing PayPal payment...');

      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate booking reference
      const bookingRef = generateReservationCode();

      // Show success toast (fixed: removed duplicate emoji)
      toast.success('Payment successful! Your verified flight reservation is ready to download.');

      // Navigate to success page
      setTimeout(() => {
        navigate('/success', {
          state: {
            bookingReference: bookingRef,
            paymentData: { success: true, paymentId: `paypal_${Date.now()}`, method: 'paypal' },
            selectedFlight: getDepartureFlightData(),
            returnFlight: getReturnFlightData(),
            passengers: bookingData?.passengers || [],
            email: bookingData?.email || '',
            tripType: bookingData?.tripType || 'oneWay',
            totalPrice: calculateTotalPrice()
          }
        });
      }, 1500);

    } catch (err) {
      console.error('❌ PayPal payment error:', err);
      setError('Payment failed. Please try again.');
      setIsProcessingPayPal(false);
    }
  };

  // Format flight time for display
  const formatFlightTime = (timeString) => {
    if (!timeString) return 'N/A';
    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch (err) {
      console.error('Error formatting time:', err);
      return 'N/A';
    }
  };

  // Format flight date for display
  const formatFlightDate = (timeString) => {
    if (!timeString) return 'N/A';
    try {
      const date = new Date(timeString);
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      });
    } catch (err) {
      console.error('Error formatting date:', err);
      return 'N/A';
    }
  };

  // Flight Details Component
  const FlightDetails = ({ flight, label, isReturn = false }) => {
    if (!flight) return null;

    const departure = flight.flight?.departure || flight.departure;
    const arrival = flight.flight?.arrival || flight.arrival;
    const airline = flight.airline;
    const flightNumber = flight.flightNumber;

    return (
      <div className="checkout-flight-card">
        <div className="flight-header">
          <div className="flight-type">
            <PaperAirplaneIcon className="h-5 w-5 mr-2" />
            <span>{label}</span>
          </div>
          <div className="flight-number">
            {airline?.name} {flightNumber}
          </div>
        </div>

        <div className="flight-route">
          <div className="airport-info">
            <div className="airport-code">{departure?.airport}</div>
            <div className="flight-time">{formatFlightTime(departure?.time)}</div>
            <div className="flight-date">{formatFlightDate(departure?.time)}</div>
          </div>

          <div className="flight-arrow">
            <div className="arrow-line"></div>
            <PaperAirplaneIcon className="h-4 w-4 text-brand-600" />
          </div>

          <div className="airport-info">
            <div className="airport-code">{arrival?.airport}</div>
            <div className="flight-time">{formatFlightTime(arrival?.time)}</div>
            <div className="flight-date">{formatFlightDate(arrival?.time)}</div>
          </div>
        </div>
      </div>
    );
  };

  // Passenger Details Component
  const PassengerDetails = ({ passengers, email }) => {
    if (!passengers || passengers.length === 0) return null;

    return (
      <div className="checkout-passenger-card">
        <div className="passenger-header">
          <div className="passenger-icon">
            <UserIcon className="h-5 w-5" />
          </div>
          <h3 className="passenger-title">Passenger Details</h3>
        </div>

        <div className="passenger-list">
          {passengers.map((passenger, index) => (
            <div key={index} className="passenger-item">
              <div className="passenger-name">
                {passenger.firstName} {passenger.lastName}
              </div>
              {passenger.dateOfBirth && (
                <div className="passenger-dob">
                  DOB: {passenger.dateOfBirth}
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="contact-info">
          <div className="contact-label">Contact Email:</div>
          <div className="contact-email">{email}</div>
        </div>
      </div>
    );
  };

  // Booking Overview Component
  const BookingOverview = ({ bookingData, onEditDetails }) => {
    const departureFlightData = getDepartureFlightData();
    const returnFlightData = getReturnFlightData();
    const passengersData = bookingData?.passengers || [];
    const emailData = bookingData?.email || '';

    return (
      <div className="checkout-booking-overview">
        <div className="flex items-center justify-between mb-10">
          <h2 className="text-2xl font-black text-brand-700 flex items-center">
            <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mr-4">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            Booking Overview
          </h2>
          <button
            onClick={onEditDetails}
            className="flex items-center text-brand-600 hover:text-brand-700 text-sm font-bold transition-all duration-300 px-4 py-2 rounded-xl hover:bg-brand-50 border border-brand-200 hover:border-brand-300"
          >
            <PencilSquareIcon className="h-5 w-5 mr-2" />
            <span>Edit Details</span>
            <div className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
              Progress Saved
            </div>
          </button>
        </div>

        <div className="space-y-6">
          {/* Flight Details */}
          {departureFlightData && (
            <FlightDetails
              flight={departureFlightData}
              label={returnFlightData ? "Outbound Flight" : "Flight"}
            />
          )}

          {returnFlightData && (
            <FlightDetails
              flight={returnFlightData}
              label="Return Flight"
              isReturn={true}
            />
          )}

          {/* Passenger Details */}
          <PassengerDetails passengers={passengersData} email={emailData} />
        </div>

        {/* Modern Confirmation Banner */}
        <div className="checkout-confirmation-banner">
          <CheckCircleIcon className="icon" />
          <p className="text">
            Once payment is successful, you'll be able to instantly download your ticket.
          </p>
        </div>
      </div>
    );
  };

  // Redesigned Payment Summary Component
  const PaymentSummary = ({ totalPrice, passengerCount, onStripePayment, onPayPalPayment, isProcessingStripe, isProcessingPayPal }) => {
    // Calculate flight count based on trip type
    const tripType = tripTypeData || 'oneWay';
    const flightLabel = tripType === 'return' ? 'Return Journey' : 'One-Way Flight Reservation';

    return (
      <div className="space-y-6">
        {/* Clean Price Display */}
        <div className="checkout-price-display">
          <div className="amount">
            ${totalPrice}
          </div>
          <div className="description">
            {flightLabel}
          </div>
        </div>

        {/* Clean Payment Buttons */}
        <div className="space-y-3">
          <motion.button
            onClick={onStripePayment}
            disabled={isProcessingStripe || isProcessingPayPal}
            className="checkout-payment-button"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isProcessingStripe ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                <span>Processing Payment...</span>
              </>
            ) : (
              <>
                <CreditCardIcon className="h-5 w-5 mr-3" />
                <span>Complete Payment – ${totalPrice}</span>
              </>
            )}
          </motion.button>

          <motion.button
            onClick={onPayPalPayment}
            disabled={isProcessingStripe || isProcessingPayPal}
            className="checkout-paypal-button"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isProcessingPayPal ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                <span>Processing Payment...</span>
              </>
            ) : (
              <>
                <span className="paypal-logo">PayPal</span>
              </>
            )}
          </motion.button>
        </div>

        {/* Clean Support Text */}
        <div className="checkout-support-text">
          <p className="text-sm text-brand-600 text-center leading-relaxed">
            Need help? <a href="mailto:<EMAIL>" className="text-brand-600 hover:underline">Contact support</a>
          </p>
        </div>
      </div>
    );
  }

  // Get flight and passenger data for display with safety checks
  let departureFlightData, returnFlightData;
  try {
    departureFlightData = getDepartureFlightData();
    returnFlightData = getReturnFlightData();
  } catch (err) {
    console.error('❌ BookingSummaryPage: Error getting flight data:', err);
    setError('Error loading flight data. Please try again.');
    return null;
  }

  const passengersData = bookingData?.passengers || [];
  const emailData = bookingData?.email || '';
  const tripTypeData = bookingData?.tripType || 'oneWay';
  const totalPrice = calculateTotalPrice();

  // Safety check for totalPrice
  if (!totalPrice || isNaN(parseFloat(totalPrice))) {
    console.error('❌ BookingSummaryPage: Invalid totalPrice:', totalPrice);
    setError('Error calculating price. Please try again.');
    return null;
  }

  // Debug logging
  console.log('BookingSummaryPage: Render data:', {
    bookingData,
    passengersData,
    emailData,
    departureFlightData,
    returnFlightData
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-50 to-accent-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-600 mx-auto mb-4"></div>
          <p className="text-brand-600 font-medium">Loading your booking details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-50 to-accent-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Booking Error</h2>
          <p className="text-gray-600 mb-8">{error}</p>

          <div className="space-y-4">
            <button
              onClick={() => navigate('/')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold"
            >
              🔍 Start New Flight Search
            </button>
            <br />
            {process.env.NODE_ENV === 'development' && (
              <>
                <button
                  onClick={() => navigate('/checkout-test')}
                  className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 text-sm mr-2"
                >
                  🧪 Go to Test Page
                </button>
                <button
                  onClick={() => window.location.href = '/checkout?test=true'}
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 text-sm"
                >
                  🔧 Load Test Data
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Main render
  return (
    <div className="min-h-screen bg-gradient-to-br from-brand-50 to-accent-50">
      <ToastContainer />

      {/* Header Section */}
      <div className="checkout-header">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="checkout-security-badge">
              Secure Checkout • Bank-Level SSL
            </div>

            <h1 className="text-2xl md:text-3xl font-bold text-brand-800 mb-4 leading-tight">
              Complete Your
              <span className="price-final">
                ${totalPrice} Reservation
              </span>
            </h1>

            <div className="max-w-4xl mx-auto mb-12">
              <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-6">
                You're just one step away from your professional, embassy-approved flight reservation.
                <strong className="text-brand-800"> Review your details and complete your secure payment.</strong>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Booking Overview */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <BookingOverview
                bookingData={bookingData}
                onEditDetails={handleEditDetails}
              />
            </motion.div>
          </div>

          {/* Right Column - Clean Payment Summary */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="checkout-payment-container"
            >
              {/* Clean Payment Header */}
              <div className="checkout-payment-header">
                <div className="icon">
                  <LockClosedIcon className="h-6 w-6 text-white" />
                </div>
                <h2 className="title">Secure Payment</h2>
                <div className="lock-badge">
                  <LockClosedIcon className="h-4 w-4 mr-2" />
                  SSL
                </div>
              </div>

              <PaymentSummary
                totalPrice={totalPrice}
                passengerCount={passengersData.length}
                onStripePayment={handleStripePayment}
                onPayPalPayment={handlePayPalPayment}
                isProcessingStripe={isProcessingStripe}
                isProcessingPayPal={isProcessingPayPal}
              />

              {/* Premium shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingSummaryPage;
