// SEO Validation Utility for Blog Posts
// Tests all blog posts for 100/100 SEO scoring standards

import { getAllBlogPosts } from '../data/blogPosts';

export const validateBlogPostSEO = (post) => {
  const issues = [];
  const warnings = [];
  const successes = [];

  // 1. Title validation
  if (!post.title) {
    issues.push('Missing title');
  } else if (post.title.length < 30) {
    warnings.push('Title too short (< 30 chars)');
  } else if (post.title.length > 60) {
    warnings.push('Title too long (> 60 chars)');
  } else {
    successes.push('Title length optimal');
  }

  // 2. Meta description validation
  if (!post.metaDescription && !post.excerpt) {
    issues.push('Missing meta description/excerpt');
  } else {
    const description = post.metaDescription || post.excerpt;
    if (description.length < 120) {
      warnings.push('Meta description too short (< 120 chars)');
    } else if (description.length > 160) {
      warnings.push('Meta description too long (> 160 chars)');
    } else {
      successes.push('Meta description length optimal');
    }
  }

  // 3. Content structure validation
  if (!post.content) {
    issues.push('Missing content');
  } else {
    // Check for proper heading hierarchy
    const headings = post.content.match(/^#{1,6}\s.+$/gm) || [];
    if (headings.length === 0) {
      warnings.push('No headings found in content');
    } else {
      successes.push(`Found ${headings.length} headings`);
    }

    // Check for FAQ sections
    const hasFAQ = post.content.toLowerCase().includes('faq') || 
                   post.content.toLowerCase().includes('frequently asked');
    if (hasFAQ) {
      successes.push('FAQ section detected');
    } else {
      warnings.push('No FAQ section found');
    }

    // Check for markdown formatting issues
    const strayHashes = post.content.match(/(?<!^|\n)#(?!\s)/g);
    if (strayHashes && strayHashes.length > 0) {
      issues.push(`Found ${strayHashes.length} stray # symbols`);
    } else {
      successes.push('No stray # symbols found');
    }

    // Check for proper bold formatting
    const improperBold = post.content.match(/\*\*[^*]+\*\*(?!\s*\n)/g);
    if (improperBold && improperBold.length > 0) {
      warnings.push(`Found ${improperBold.length} bold text without proper spacing`);
    } else {
      successes.push('Bold formatting looks good');
    }
  }

  // 4. Slug validation
  if (!post.slug) {
    issues.push('Missing slug');
  } else if (post.slug.includes(' ') || post.slug.includes('_')) {
    warnings.push('Slug contains spaces or underscores');
  } else {
    successes.push('Slug format is SEO-friendly');
  }

  // 5. Tags validation
  if (!post.tags || post.tags.length === 0) {
    warnings.push('No tags specified');
  } else if (post.tags.length > 10) {
    warnings.push('Too many tags (> 10)');
  } else {
    successes.push(`${post.tags.length} tags specified`);
  }

  // 6. Publish date validation
  if (!post.publishDate) {
    warnings.push('Missing publish date');
  } else {
    successes.push('Publish date specified');
  }

  // Calculate SEO score
  const totalChecks = 15; // Total number of checks
  const issueWeight = 3; // Issues are worth 3 points each
  const warningWeight = 1; // Warnings are worth 1 point each
  
  const deductions = (issues.length * issueWeight) + (warnings.length * warningWeight);
  const score = Math.max(0, Math.min(100, 100 - (deductions * (100 / totalChecks))));

  return {
    score: Math.round(score),
    issues,
    warnings,
    successes,
    grade: score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F'
  };
};

export const validateAllBlogPosts = () => {
  const posts = getAllBlogPosts();
  const results = posts.map(post => ({
    title: post.title,
    slug: post.slug,
    validation: validateBlogPostSEO(post)
  }));

  // Calculate overall statistics
  const totalScore = results.reduce((sum, result) => sum + result.validation.score, 0);
  const averageScore = Math.round(totalScore / results.length);
  
  const gradeDistribution = results.reduce((dist, result) => {
    const grade = result.validation.grade;
    dist[grade] = (dist[grade] || 0) + 1;
    return dist;
  }, {});

  const totalIssues = results.reduce((sum, result) => sum + result.validation.issues.length, 0);
  const totalWarnings = results.reduce((sum, result) => sum + result.validation.warnings.length, 0);

  return {
    results,
    summary: {
      totalPosts: results.length,
      averageScore,
      gradeDistribution,
      totalIssues,
      totalWarnings,
      postsWithIssues: results.filter(r => r.validation.issues.length > 0).length,
      postsWithWarnings: results.filter(r => r.validation.warnings.length > 0).length,
      perfectPosts: results.filter(r => r.validation.score === 100).length
    }
  };
};

export const generateSEOReport = () => {
  const validation = validateAllBlogPosts();
  
  console.log('=== VerifiedOnward Blog SEO Report ===');
  console.log(`Total Posts: ${validation.summary.totalPosts}`);
  console.log(`Average Score: ${validation.summary.averageScore}/100`);
  console.log(`Grade Distribution:`, validation.summary.gradeDistribution);
  console.log(`Total Issues: ${validation.summary.totalIssues}`);
  console.log(`Total Warnings: ${validation.summary.totalWarnings}`);
  console.log(`Perfect Posts (100/100): ${validation.summary.perfectPosts}`);
  console.log('');

  // Show posts with issues
  const postsWithIssues = validation.results.filter(r => r.validation.issues.length > 0);
  if (postsWithIssues.length > 0) {
    console.log('=== Posts with Issues ===');
    postsWithIssues.forEach(post => {
      console.log(`${post.title} (${post.validation.score}/100):`);
      post.validation.issues.forEach(issue => console.log(`  ❌ ${issue}`));
      console.log('');
    });
  }

  // Show posts with warnings
  const postsWithWarnings = validation.results.filter(r => r.validation.warnings.length > 0);
  if (postsWithWarnings.length > 0) {
    console.log('=== Posts with Warnings ===');
    postsWithWarnings.forEach(post => {
      console.log(`${post.title} (${post.validation.score}/100):`);
      post.validation.warnings.forEach(warning => console.log(`  ⚠️ ${warning}`));
      console.log('');
    });
  }

  // Show perfect posts
  const perfectPosts = validation.results.filter(r => r.validation.score === 100);
  if (perfectPosts.length > 0) {
    console.log('=== Perfect Posts (100/100) ===');
    perfectPosts.forEach(post => {
      console.log(`✅ ${post.title}`);
    });
  }

  return validation;
};

// Test specific markdown formatting issues
export const testMarkdownFormatting = (content) => {
  const tests = {
    strayHashes: {
      pattern: /(?<!^|\n)#(?!\s)/g,
      description: 'Stray # symbols not at line start'
    },
    improperBold: {
      pattern: /\*\*[^*]+\*\*(?!\s*\n)/g,
      description: 'Bold text without proper line breaks'
    },
    multipleLineBreaks: {
      pattern: /\n{3,}/g,
      description: 'Multiple consecutive line breaks'
    },
    trailingSpaces: {
      pattern: /[ \t]+$/gm,
      description: 'Trailing spaces at end of lines'
    },
    improperHeadings: {
      pattern: /^(#{1,6})(?!\s)/gm,
      description: 'Headings without space after #'
    }
  };

  const results = {};
  Object.entries(tests).forEach(([key, test]) => {
    const matches = content.match(test.pattern);
    results[key] = {
      found: matches ? matches.length : 0,
      description: test.description,
      samples: matches ? matches.slice(0, 3) : []
    };
  });

  return results;
};
