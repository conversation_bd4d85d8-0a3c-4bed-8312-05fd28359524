// Hybrid SEO Utilities - Google + AI Search Engine Optimization
// Handles meta tags, structured data, and AI-friendly content optimization

// Generate comprehensive meta tags for blog posts
export const generateMetaTags = (post) => {
  const metaTags = [
    // Basic meta tags
    { name: 'description', content: post.metaDescription || post.excerpt },
    { name: 'keywords', content: post.metaKeywords?.join(', ') || '' },
    { name: 'author', content: 'VerifiedOnward Travel Expert' },
    { name: 'robots', content: 'index, follow' },
    
    // Open Graph tags
    { property: 'og:title', content: post.metaTitle || post.title },
    { property: 'og:description', content: post.metaDescription || post.excerpt },
    { property: 'og:type', content: 'article' },
    { property: 'og:url', content: `https://verifiedonward.com/blog/${post.slug}` },
    { property: 'og:site_name', content: 'VerifiedOnward' },
    { property: 'og:locale', content: 'en_US' },
    
    // Twitter Card tags
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: post.metaTitle || post.title },
    { name: 'twitter:description', content: post.metaDescription || post.excerpt },
    { name: 'twitter:site', content: '@VerifiedOnward' },
    
    // Article-specific tags
    { property: 'article:published_time', content: post.publishDate },
    { property: 'article:modified_time', content: post.publishDate },
    { property: 'article:author', content: 'VerifiedOnward Travel Expert' },
    { property: 'article:section', content: 'Travel Guides' },
    { property: 'article:tag', content: post.tags?.join(', ') || '' }
  ];

  return metaTags;
};

// Generate Article structured data
export const generateArticleSchema = (post) => {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": post.featuredImage || "https://verifiedonward.com/og-image.jpg",
    "author": {
      "@type": "Person",
      "name": "VerifiedOnward Travel Expert",
      "url": "https://verifiedonward.com/about"
    },
    "publisher": {
      "@type": "Organization",
      "name": "VerifiedOnward",
      "logo": {
        "@type": "ImageObject",
        "url": "https://verifiedonward.com/logo.png",
        "width": 200,
        "height": 60
      }
    },
    "datePublished": post.publishDate,
    "dateModified": post.publishDate,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://verifiedonward.com/blog/${post.slug}`
    },
    "keywords": post.metaKeywords?.join(', ') || '',
    "articleSection": "Travel Guides",
    "wordCount": post.content?.length || 0,
    "inLanguage": "en-US"
  };
};

// Generate FAQ structured data
export const generateFAQSchema = (faqs) => {
  if (!faqs || faqs.length === 0) return null;

  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
};

// Generate HowTo structured data for step-by-step guides
export const generateHowToSchema = (post, steps) => {
  if (!steps || steps.length === 0) return null;

  return {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": post.title,
    "description": post.excerpt,
    "image": post.featuredImage || "https://verifiedonward.com/og-image.jpg",
    "totalTime": post.readTime,
    "supply": [
      "Valid passport",
      "Passport photos",
      "Financial documents",
      "Flight reservation"
    ],
    "tool": [
      "Computer or smartphone",
      "Internet connection",
      "Scanner or camera"
    ],
    "step": steps.map((step, index) => ({
      "@type": "HowToStep",
      "position": index + 1,
      "name": step.title,
      "text": step.description,
      "url": `https://verifiedonward.com/blog/${post.slug}#step-${index + 1}`
    }))
  };
};

// Generate Breadcrumb structured data
export const generateBreadcrumbSchema = (post) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://verifiedonward.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Blog",
        "item": "https://verifiedonward.com/blog"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": post.title,
        "item": `https://verifiedonward.com/blog/${post.slug}`
      }
    ]
  };
};

// Parse FAQs from content
export const parseFAQsFromContent = (content) => {
  const faqRegex = /### Q: (.+?)\n✅ (.+?)(?=\n### Q:|$)/gs;
  const faqs = [];
  let match;
  
  while ((match = faqRegex.exec(content)) !== null) {
    faqs.push({
      question: match[1].trim(),
      answer: match[2].trim()
    });
  }
  
  return faqs;
};

// Parse steps from content
export const parseStepsFromContent = (content) => {
  const stepRegex = /### (\d+️⃣.+?)\n\n(.+?)(?=\n### \d+️⃣|$)/gs;
  const steps = [];
  let match;
  
  while ((match = stepRegex.exec(content)) !== null) {
    steps.push({
      title: match[1].trim(),
      description: match[2].trim()
    });
  }
  
  return steps;
};

// Generate all structured data for a blog post
export const generateAllStructuredData = (post) => {
  const faqs = parseFAQsFromContent(post.content);
  const steps = parseStepsFromContent(post.content);
  
  const schemas = [
    generateArticleSchema(post),
    generateBreadcrumbSchema(post)
  ];

  if (faqs.length > 0) {
    schemas.push(generateFAQSchema(faqs));
  }

  if (steps.length > 0) {
    schemas.push(generateHowToSchema(post, steps));
  }

  return schemas.filter(schema => schema !== null);
};

// AI-friendly content optimization
export const optimizeContentForAI = (content) => {
  // Add clear section markers for AI parsing
  let optimizedContent = content;

  // Ensure proper heading hierarchy
  optimizedContent = optimizedContent.replace(/^# /gm, '## ');
  
  // Add structured markers for key information
  optimizedContent = optimizedContent.replace(
    /✅ (.+)/g, 
    '<strong class="ai-highlight">✅ $1</strong>'
  );
  
  optimizedContent = optimizedContent.replace(
    /❌ (.+)/g, 
    '<strong class="ai-warning">❌ $1</strong>'
  );

  return optimizedContent;
};

// Generate canonical URL
export const generateCanonicalURL = (slug) => {
  return `https://verifiedonward.com/blog/${slug}`;
};

// Calculate reading time
export const calculateReadingTime = (content) => {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return `${minutes} min read`;
};

// SEO score calculator
export const calculateSEOScore = (post) => {
  let score = 0;
  const checks = [];

  // Title optimization (max 60 chars)
  if (post.title && post.title.length <= 60) {
    score += 10;
    checks.push({ name: 'Title length', status: 'pass' });
  } else {
    checks.push({ name: 'Title length', status: 'fail', message: 'Title should be under 60 characters' });
  }

  // Meta description (150-160 chars)
  if (post.metaDescription && post.metaDescription.length >= 150 && post.metaDescription.length <= 160) {
    score += 10;
    checks.push({ name: 'Meta description', status: 'pass' });
  } else {
    checks.push({ name: 'Meta description', status: 'fail', message: 'Meta description should be 150-160 characters' });
  }

  // Keywords present
  if (post.metaKeywords && post.metaKeywords.length > 0) {
    score += 10;
    checks.push({ name: 'Keywords', status: 'pass' });
  } else {
    checks.push({ name: 'Keywords', status: 'fail', message: 'Add meta keywords' });
  }

  // Content length (min 1000 words)
  const wordCount = post.content ? post.content.split(/\s+/).length : 0;
  if (wordCount >= 1000) {
    score += 15;
    checks.push({ name: 'Content length', status: 'pass' });
  } else {
    checks.push({ name: 'Content length', status: 'fail', message: 'Content should be at least 1000 words' });
  }

  // Headings structure
  const hasH2 = /^## /gm.test(post.content);
  if (hasH2) {
    score += 10;
    checks.push({ name: 'Heading structure', status: 'pass' });
  } else {
    checks.push({ name: 'Heading structure', status: 'fail', message: 'Add H2 headings for better structure' });
  }

  // FAQ section
  const hasFAQ = /### Q: /g.test(post.content);
  if (hasFAQ) {
    score += 15;
    checks.push({ name: 'FAQ section', status: 'pass' });
  } else {
    checks.push({ name: 'FAQ section', status: 'warning', message: 'Consider adding FAQ section for AI optimization' });
  }

  // Internal links (basic check)
  const hasInternalLinks = /\[.+\]\(\//.test(post.content);
  if (hasInternalLinks) {
    score += 10;
    checks.push({ name: 'Internal links', status: 'pass' });
  } else {
    checks.push({ name: 'Internal links', status: 'warning', message: 'Add internal links to related content' });
  }

  // Lists and bullet points
  const hasLists = /^[\-\*\+] |^\d+\. |^✅|^❌/gm.test(post.content);
  if (hasLists) {
    score += 10;
    checks.push({ name: 'Lists and bullets', status: 'pass' });
  } else {
    checks.push({ name: 'Lists and bullets', status: 'warning', message: 'Add bullet points or lists for better readability' });
  }

  // Step-by-step content
  const hasSteps = /\d+️⃣/.test(post.content);
  if (hasSteps) {
    score += 10;
    checks.push({ name: 'Step-by-step content', status: 'pass' });
  } else {
    checks.push({ name: 'Step-by-step content', status: 'info', message: 'Consider adding numbered steps if applicable' });
  }

  return {
    score,
    maxScore: 100,
    percentage: Math.round((score / 100) * 100),
    checks,
    grade: score >= 80 ? 'A' : score >= 60 ? 'B' : score >= 40 ? 'C' : 'D'
  };
};

export default {
  generateMetaTags,
  generateArticleSchema,
  generateFAQSchema,
  generateHowToSchema,
  generateBreadcrumbSchema,
  generateAllStructuredData,
  parseFAQsFromContent,
  parseStepsFromContent,
  optimizeContentForAI,
  generateCanonicalURL,
  calculateReadingTime,
  calculateSEOScore
};
