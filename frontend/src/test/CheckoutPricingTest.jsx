import React from 'react';

const CheckoutPricingTest = () => {
  // Test the pricing logic directly (matches CheckoutPageRobustFixed logic)
  const calculateTotalPrice = (bookingData) => {
    if (!bookingData) return 4.99;

    console.log('🔍 calculateTotalPrice: Checking booking data:', {
      tripType: bookingData.tripType,
      selectedFlight: !!bookingData.selectedFlight,
      selectedOutboundFlight: !!bookingData.selectedOutboundFlight,
      selectedReturnFlight: !!bookingData.selectedReturnFlight,
      flight: !!bookingData.flight,
      outboundFlight: !!bookingData.outboundFlight,
      returnFlight: !!bookingData.returnFlight
    });

    // Calculate price based on trip type and actual flight selections
    const tripType = bookingData.tripType || 'oneWay';

    if (tripType === 'return' || tripType === 'roundTrip') {
      // For return trips, check if we have both outbound and return flights
      // Support both BookingContext format and SearchResultsPage navigation format
      const hasOutbound = bookingData.selectedOutboundFlight ||
                         bookingData.selectedFlight ||
                         bookingData.outboundFlight;
      const hasReturn = bookingData.selectedReturnFlight ||
                       bookingData.returnFlight;

      console.log('🔍 calculateTotalPrice: Return trip - hasOutbound:', !!hasOutbound, 'hasReturn:', !!hasReturn);

      if (hasOutbound && hasReturn) {
        console.log('✅ calculateTotalPrice: Return trip with both flights - $9.98');
        return 9.98;
      } else if (hasOutbound) {
        console.log('⚠️ calculateTotalPrice: Return trip with only outbound flight - $4.99');
        return 4.99;
      }
    }

    // One-way trip or fallback
    console.log('✅ calculateTotalPrice: One-way trip - $4.99');
    return 4.99;
  };

  // Test scenarios
  const testScenarios = [
    {
      name: 'One-way with selectedFlight',
      data: {
        tripType: 'oneWay',
        selectedFlight: { id: 'flight1', airline: 'Emirates' },
        selectedOutboundFlight: null,
        selectedReturnFlight: null
      },
      expected: 4.99
    },
    {
      name: 'One-way with selectedOutboundFlight',
      data: {
        tripType: 'oneWay',
        selectedFlight: null,
        selectedOutboundFlight: { id: 'flight1', airline: 'Emirates' },
        selectedReturnFlight: null
      },
      expected: 4.99
    },
    {
      name: 'Return with both outbound and return flights',
      data: {
        tripType: 'return',
        selectedFlight: null,
        selectedOutboundFlight: { id: 'flight1', airline: 'Emirates' },
        selectedReturnFlight: { id: 'flight2', airline: 'Emirates' }
      },
      expected: 9.98
    },
    {
      name: 'Return with only outbound flight',
      data: {
        tripType: 'return',
        selectedFlight: null,
        selectedOutboundFlight: { id: 'flight1', airline: 'Emirates' },
        selectedReturnFlight: null
      },
      expected: 4.99
    },
    {
      name: 'Return with selectedFlight and return flight',
      data: {
        tripType: 'return',
        selectedFlight: { id: 'flight1', airline: 'Emirates' },
        selectedOutboundFlight: null,
        selectedReturnFlight: { id: 'flight2', airline: 'Emirates' }
      },
      expected: 9.98
    },
    {
      name: 'SearchResultsPage format - One-way with flight',
      data: {
        tripType: 'oneWay',
        flight: { id: 'flight1', airline: 'Emirates' },
        selectedFlight: null,
        selectedOutboundFlight: null,
        selectedReturnFlight: null
      },
      expected: 4.99
    },
    {
      name: 'SearchResultsPage format - Return with outboundFlight and returnFlight',
      data: {
        tripType: 'return',
        outboundFlight: { id: 'flight1', airline: 'Emirates' },
        returnFlight: { id: 'flight2', airline: 'Emirates' },
        selectedFlight: null,
        selectedOutboundFlight: null,
        selectedReturnFlight: null
      },
      expected: 9.98
    },
    {
      name: 'SearchResultsPage format - Return with only outboundFlight',
      data: {
        tripType: 'return',
        outboundFlight: { id: 'flight1', airline: 'Emirates' },
        returnFlight: null,
        selectedFlight: null,
        selectedOutboundFlight: null,
        selectedReturnFlight: null
      },
      expected: 4.99
    }
  ];

  const runTests = () => {
    console.log('🧪 Running CheckoutPricingTest...');
    
    testScenarios.forEach((scenario, index) => {
      const result = calculateTotalPrice(scenario.data);
      const passed = result === scenario.expected;
      
      console.log(`Test ${index + 1}: ${scenario.name}`);
      console.log(`  Expected: $${scenario.expected}`);
      console.log(`  Got: $${result}`);
      console.log(`  Status: ${passed ? '✅ PASS' : '❌ FAIL'}`);
      console.log('---');
    });
  };

  React.useEffect(() => {
    runTests();
  }, []);

  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-4">Checkout Pricing Test</h1>
      <p className="mb-4">Check the browser console for test results.</p>
      <button 
        onClick={runTests}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Run Tests Again
      </button>
    </div>
  );
};

export default CheckoutPricingTest;
