// Hybrid SEO Blog Post Template - Ready to use for new articles
// Optimized for Google SEO + AI Search Engines (ChatGPT, Perplexity, Gemini, etc.)

export const createHybridSEOBlogPost = ({
  id,
  slug,
  primaryKeyword,
  title,
  metaTitle,
  metaDescription,
  metaKeywords,
  excerpt,
  tags,
  publishDate,
  readTime,
  thumbnail,
  sections
}) => {
  return {
    id,
    slug,
    title,
    metaTitle: metaTitle || `${title} | VerifiedOnward`,
    metaDescription: metaDescription || excerpt,
    metaKeywords: metaKeywords || [],
    excerpt,
    tags,
    publishDate,
    readTime,
    thumbnail,
    content: generateHybridSEOContent(sections, primaryKeyword)
  };
};

// Generate SEO-optimized content structure
const generateHybridSEOContent = (sections, primaryKeyword) => {
  let content = '';

  sections.forEach(section => {
    switch (section.type) {
      case 'introduction':
        content += generateIntroduction(section, primaryKeyword);
        break;
      case 'definition':
        content += generateDefinitionSection(section);
        break;
      case 'steps':
        content += generateStepsSection(section);
        break;
      case 'mistakes':
        content += generateMistakesSection(section);
        break;
      case 'country-specific':
        content += generateCountrySection(section);
        break;
      case 'expert-tips':
        content += generateExpertTipsSection(section);
        break;
      case 'faq':
        content += generateFAQSection(section);
        break;
      case 'conclusion':
        content += generateConclusionSection(section);
        break;
      default:
        content += generateGenericSection(section);
    }
  });

  return content.trim();
};

// Section generators with proper SEO structure
const generateIntroduction = (section, primaryKeyword) => {
  return `${section.content}

${primaryKeyword} is essential for visa applications, and this expert guide will show you exactly how to do it safely and effectively.

`;
};

const generateDefinitionSection = (section) => {
  return `## ${section.title}

${section.content}

✅ Embassy-approved for visa applications
✅ Includes real airline data & PNR
✅ No need to buy an actual flight yet

${section.additionalInfo || ''}

`;
};

const generateStepsSection = (section) => {
  let content = `## ${section.title}

`;

  section.steps.forEach((step, index) => {
    content += `### ${index + 1}️⃣ ${step.title}

${step.description}

${step.details ? step.details.map(detail => `- ${detail}`).join('\n') + '\n' : ''}
`;
  });

  return content + '\n';
};

const generateMistakesSection = (section) => {
  let content = `## ${section.title}

`;

  section.mistakes.forEach(mistake => {
    content += `❌ **${mistake.title}** - ${mistake.description}
✅ **Solution:** ${mistake.solution}

`;
  });

  return content + '\n';
};

const generateCountrySection = (section) => {
  let content = `## ${section.title}

`;

  if (section.countries) {
    section.countries.forEach(country => {
      content += `### ${country.name}

- **Visa requirement:** ${country.visaRequired ? 'Required' : 'Not required'}
- **Stay duration:** ${country.stayDuration}
- **Processing time:** ${country.processingTime}
- **Required documents:** ${country.documents.join(', ')}

`;
    });
  }

  return content + '\n';
};

const generateExpertTipsSection = (section) => {
  let content = `## ${section.title}

`;

  section.tips.forEach(tip => {
    content += `✅ **${tip.title}** - ${tip.description}

`;
  });

  return content + '\n';
};

const generateFAQSection = (section) => {
  let content = `## ${section.title}

`;

  section.faqs.forEach(faq => {
    content += `### Q: ${faq.question}
✅ ${faq.answer}

`;
  });

  return content + '\n';
};

const generateConclusionSection = (section) => {
  return `## ${section.title}

${section.content}

${section.keyTakeaways ? section.keyTakeaways.map(takeaway => `✅ ${takeaway}`).join('\n') : ''}

`;
};

const generateGenericSection = (section) => {
  return `## ${section.title}

${section.content}

`;
};

// Example template usage
export const exampleBlogPost = createHybridSEOBlogPost({
  id: 999,
  slug: 'tourist-visa-requirements-2025',
  primaryKeyword: 'Tourist Visa Requirements',
  title: 'Tourist Visa Requirements: Complete Country-by-Country Guide 2025',
  metaTitle: 'Tourist Visa Requirements 2025 - Complete Guide by Country',
  metaDescription: 'Complete guide to tourist visa requirements by country in 2025. Requirements, documents, processing times, and expert tips for successful applications.',
  metaKeywords: ['tourist visa requirements', 'visa guide 2025', 'country visa requirements', 'travel visa guide'],
  excerpt: 'Planning international travel? This comprehensive guide covers tourist visa requirements for every major destination, including documents needed, processing times, and expert tips for approval.',
  tags: ['Visa Guide', 'Travel Requirements', 'Tourism'],
  publishDate: '2025-01-20',
  readTime: '12 min read',
  thumbnail: {
    type: 'gradient',
    colors: ['from-blue-500', 'to-purple-600'],
    icon: '🌍',
    pattern: 'travel'
  },
  sections: [
    {
      type: 'introduction',
      content: 'Planning international travel can be exciting, but navigating visa requirements can be overwhelming. Different countries have different rules, processing times, and document requirements.'
    },
    {
      type: 'definition',
      title: 'What is a Tourist Visa?',
      content: 'A tourist visa is an official document that allows you to enter a foreign country for leisure, sightseeing, or visiting friends and family.',
      additionalInfo: 'Tourist visas are typically short-term and do not permit work or study activities.'
    },
    {
      type: 'steps',
      title: 'Step-by-Step Visa Application Process',
      steps: [
        {
          title: 'Research Requirements',
          description: 'Check the specific visa requirements for your destination country.',
          details: ['Visit official embassy websites', 'Check processing times', 'Verify document requirements']
        },
        {
          title: 'Gather Documents',
          description: 'Collect all required documents including passport, photos, and supporting materials.',
          details: ['Valid passport (6+ months validity)', 'Recent passport photos', 'Flight reservations', 'Hotel bookings', 'Financial statements']
        },
        {
          title: 'Submit Application',
          description: 'Submit your complete application through the appropriate channel.',
          details: ['Online application portal', 'Embassy appointment', 'Visa application center']
        }
      ]
    },
    {
      type: 'mistakes',
      title: 'Common Mistakes and How to Avoid Them',
      mistakes: [
        {
          title: 'Insufficient Financial Proof',
          description: 'Not providing adequate evidence of financial stability',
          solution: 'Include bank statements, employment letters, and proof of income'
        },
        {
          title: 'Invalid Flight Reservations',
          description: 'Using fake or expired flight bookings',
          solution: 'Use embassy-approved flight reservation services'
        }
      ]
    },
    {
      type: 'faq',
      title: 'Mini FAQ',
      faqs: [
        {
          question: 'How long does visa processing take?',
          answer: 'Processing times vary by country, typically 5-15 business days for tourist visas.'
        },
        {
          question: 'Can I work on a tourist visa?',
          answer: 'No, tourist visas prohibit employment and business activities.'
        }
      ]
    },
    {
      type: 'conclusion',
      title: 'Conclusion',
      content: 'Successfully obtaining a tourist visa requires careful preparation and attention to detail. By following this guide and avoiding common mistakes, you can increase your chances of approval.',
      keyTakeaways: [
        'Research requirements thoroughly before applying',
        'Provide complete and accurate documentation',
        'Apply well in advance of your travel dates',
        'Use professional services for flight reservations'
      ]
    }
  ]
});

// Export the template for easy use
export default {
  createHybridSEOBlogPost,
  exampleBlogPost
};
