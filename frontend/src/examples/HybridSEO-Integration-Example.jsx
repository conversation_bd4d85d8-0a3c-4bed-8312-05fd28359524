// Example: How to integrate Hybrid SEO Blog System with existing VerifiedOnward setup
// This file shows the complete integration process

import React from 'react';
import { Routes, Route } from 'react-router-dom';

// Import the new hybrid SEO components
import HybridSEOBlogPostPage from '../pages/HybridSEOBlogPostPage';
import HybridSEOBlogTemplate from '../components/HybridSEOBlogTemplate';
import { createHybridSEOBlogPost } from '../templates/HybridSEOBlogPostTemplate';
import { 
  generateAllStructuredData, 
  calculateSEOScore,
  generateMetaTags 
} from '../utils/hybridSEO';

// Example: Update your existing routing
const AppRoutes = () => {
  return (
    <Routes>
      {/* Existing routes */}
      <Route path="/" element={<HomePage />} />
      <Route path="/blog" element={<BlogPage />} />
      
      {/* NEW: Use Hybrid SEO Blog Post Page */}
      <Route path="/blog/:slug" element={<HybridSEOBlogPostPage />} />
      
      {/* Other existing routes */}
      <Route path="/checkout" element={<CheckoutPage />} />
    </Routes>
  );
};

// Example: Create a new blog post using the template system
const createNewBlogPost = () => {
  const newPost = createHybridSEOBlogPost({
    id: 999,
    slug: 'uk-visa-requirements-2025',
    primaryKeyword: 'UK Visa Requirements',
    title: 'UK Visa Requirements: Complete Guide for 2025',
    metaTitle: 'UK Visa Requirements 2025 - Complete Application Guide',
    metaDescription: 'Complete guide to UK visa requirements in 2025. Documents, processing times, fees, and expert tips for successful visa applications to the United Kingdom.',
    metaKeywords: [
      'uk visa requirements',
      'uk visa application 2025',
      'united kingdom visa guide',
      'uk tourist visa',
      'uk visa documents'
    ],
    excerpt: 'Planning to visit the UK? This comprehensive guide covers all UK visa requirements for 2025, including documents needed, processing times, fees, and expert tips for approval.',
    tags: ['UK Visa', 'Visa Guide', 'Travel Requirements'],
    publishDate: '2025-01-20',
    readTime: '15 min read',
    thumbnail: {
      type: 'gradient',
      colors: ['from-red-500', 'to-blue-600'],
      icon: '🇬🇧',
      pattern: 'uk'
    },
    sections: [
      {
        type: 'introduction',
        content: 'The United Kingdom remains one of the world\'s most popular travel destinations, attracting millions of visitors each year. However, navigating UK visa requirements can be complex, with different rules for different nationalities and purposes of visit.'
      },
      {
        type: 'definition',
        title: 'What is a UK Visa?',
        content: 'A UK visa is an official document that grants permission to enter, stay, or transit through the United Kingdom for a specific period and purpose.',
        additionalInfo: 'The type of visa you need depends on your nationality, the purpose of your visit, and how long you plan to stay.'
      },
      {
        type: 'steps',
        title: 'Step-by-Step UK Visa Application Process',
        steps: [
          {
            title: 'Determine Visa Type',
            description: 'Identify which UK visa category applies to your travel purpose.',
            details: [
              'Standard Visitor visa (tourism, business, family visits)',
              'Transit visa (passing through UK airports)',
              'Work visa (employment purposes)',
              'Study visa (educational purposes)'
            ]
          },
          {
            title: 'Check Eligibility',
            description: 'Verify you meet the requirements for your chosen visa type.',
            details: [
              'Valid passport (6+ months remaining)',
              'Sufficient funds for your stay',
              'Clear travel purpose and itinerary',
              'No immigration violations history'
            ]
          },
          {
            title: 'Gather Required Documents',
            description: 'Collect all necessary supporting documents for your application.',
            details: [
              'Completed visa application form',
              'Passport-sized photographs',
              'Bank statements (3-6 months)',
              'Employment letter or business documents',
              'Travel itinerary and accommodation bookings',
              'Flight reservations'
            ]
          },
          {
            title: 'Submit Application',
            description: 'Apply online and book your biometric appointment.',
            details: [
              'Complete online application form',
              'Pay visa application fee',
              'Book biometric appointment',
              'Submit documents at visa center'
            ]
          },
          {
            title: 'Attend Biometric Appointment',
            description: 'Provide fingerprints and photograph at the visa application center.',
            details: [
              'Arrive on time for your appointment',
              'Bring all required documents',
              'Provide biometric data',
              'Submit passport for processing'
            ]
          }
        ]
      },
      {
        type: 'mistakes',
        title: 'Common UK Visa Application Mistakes',
        mistakes: [
          {
            title: 'Insufficient Financial Evidence',
            description: 'Not providing adequate proof of funds to support your stay',
            solution: 'Include 3-6 months of bank statements showing consistent income and sufficient savings'
          },
          {
            title: 'Incomplete Travel Documentation',
            description: 'Missing or invalid flight reservations and accommodation bookings',
            solution: 'Use professional flight reservation services and confirmed hotel bookings'
          },
          {
            title: 'Poor Application Form Completion',
            description: 'Inconsistent information or missing details in the application',
            solution: 'Double-check all information matches your supporting documents exactly'
          },
          {
            title: 'Inadequate Travel Purpose Explanation',
            description: 'Vague or unconvincing reasons for visiting the UK',
            solution: 'Provide detailed itinerary with specific places, dates, and activities planned'
          }
        ]
      },
      {
        type: 'faq',
        title: 'UK Visa FAQ',
        faqs: [
          {
            question: 'How long does UK visa processing take?',
            answer: 'Standard processing time is 3 weeks for applications from outside the UK, though this can vary by country and season.'
          },
          {
            question: 'Can I work on a UK tourist visa?',
            answer: 'No, Standard Visitor visas do not permit employment. You can only engage in permitted business activities like meetings or conferences.'
          },
          {
            question: 'How much money do I need to show for a UK visa?',
            answer: 'There\'s no fixed amount, but you should demonstrate you can afford your entire trip. Generally, £1,000-2,000 per month of stay is recommended.'
          },
          {
            question: 'Do I need travel insurance for a UK visa?',
            answer: 'While not mandatory, comprehensive travel insurance is highly recommended and can strengthen your application.'
          },
          {
            question: 'Can I extend my UK tourist visa?',
            answer: 'Standard Visitor visas cannot usually be extended. You must leave the UK and apply for a new visa if you wish to return.'
          }
        ]
      },
      {
        type: 'conclusion',
        title: 'Key Takeaways for UK Visa Success',
        content: 'Successfully obtaining a UK visa requires careful preparation, complete documentation, and honest representation of your travel intentions. The key is demonstrating strong ties to your home country and genuine reasons for visiting the UK.',
        keyTakeaways: [
          'Choose the correct visa type for your travel purpose',
          'Provide comprehensive financial documentation',
          'Submit genuine, professional travel reservations',
          'Be honest and consistent in all application materials',
          'Apply well in advance of your intended travel date',
          'Consider using professional services for complex applications'
        ]
      }
    ]
  });

  return newPost;
};

// Example: How to use SEO utilities in your components
const BlogPostWithSEOAnalysis = ({ post }) => {
  // Calculate SEO score
  const seoScore = calculateSEOScore(post);
  
  // Generate structured data
  const schemas = generateAllStructuredData(post);
  
  // Generate meta tags
  const metaTags = generateMetaTags(post);
  
  // Log results (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 SEO Analysis:', {
      score: seoScore,
      schemas: schemas.length,
      metaTags: metaTags.length
    });
  }
  
  return <HybridSEOBlogTemplate post={post} />;
};

// Example: Migration guide for existing blog posts
const migrateBlogPost = (existingPost) => {
  // Add missing SEO fields to existing blog posts
  return {
    ...existingPost,
    metaTitle: existingPost.metaTitle || `${existingPost.title} | VerifiedOnward`,
    metaDescription: existingPost.metaDescription || existingPost.excerpt,
    metaKeywords: existingPost.metaKeywords || extractKeywordsFromContent(existingPost.content),
    // Ensure content follows hybrid SEO structure
    content: optimizeContentStructure(existingPost.content)
  };
};

// Helper function to extract keywords from content
const extractKeywordsFromContent = (content) => {
  // Simple keyword extraction (you might want to use a more sophisticated approach)
  const words = content.toLowerCase().match(/\b\w{4,}\b/g) || [];
  const frequency = {};
  
  words.forEach(word => {
    frequency[word] = (frequency[word] || 0) + 1;
  });
  
  return Object.entries(frequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word);
};

// Helper function to optimize content structure
const optimizeContentStructure = (content) => {
  let optimized = content;
  
  // Ensure proper heading hierarchy
  optimized = optimized.replace(/^# /gm, '## ');
  
  // Add FAQ section if missing
  if (!optimized.includes('## Mini FAQ') && !optimized.includes('## FAQ')) {
    optimized += `

## Mini FAQ

### Q: Is this information up to date?
✅ Yes, this guide is updated for 2025 with the latest requirements.

### Q: Where can I get professional help?
✅ VerifiedOnward provides expert assistance with visa documentation and flight reservations.
`;
  }
  
  return optimized;
};

// Example: Complete integration checklist
const integrationChecklist = {
  components: [
    '✅ HybridSEOBlogTemplate.jsx - Main blog template component',
    '✅ HybridSEOBlogPostPage.jsx - Blog post page with SEO integration',
    '✅ HybridSEOBlogPostTemplate.js - Blog post creation template',
    '✅ hybridSEO.js - SEO utilities and helpers'
  ],
  routing: [
    '✅ Update blog post routes to use HybridSEOBlogPostPage',
    '✅ Ensure proper URL structure (/blog/:slug)',
    '✅ Add canonical URL handling',
    '✅ Implement proper 404 handling for missing posts'
  ],
  seo: [
    '✅ Meta tags generation and injection',
    '✅ Structured data (Article, FAQ, HowTo, Breadcrumb)',
    '✅ Open Graph and Twitter Card tags',
    '✅ Canonical URLs',
    '✅ SEO score calculation and monitoring'
  ],
  content: [
    '✅ Hybrid SEO content structure',
    '✅ AI-friendly formatting',
    '✅ FAQ sections with structured data',
    '✅ Step-by-step guides with proper markup',
    '✅ Expert tips and credibility signals'
  ],
  performance: [
    '✅ Lazy loading for images',
    '✅ Optimized animations and transitions',
    '✅ Mobile-responsive design',
    '✅ Fast loading times',
    '✅ Proper error handling'
  ]
};

export default {
  AppRoutes,
  createNewBlogPost,
  BlogPostWithSEOAnalysis,
  migrateBlogPost,
  integrationChecklist
};
