import React, { useState } from 'react';
import { 
  EnvelopeIcon, 
  UserIcon, 
  PlusIcon, 
  TrashIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';

const PassengerDetailsSectionNew = ({
  onSubmit,
  isLoading = false,
  submitButtonText = "Continue to Payment",
  className = ""
}) => {
  // State management
  const [email, setEmail] = useState('');
  const [passengers, setPassengers] = useState([{ firstName: '', lastName: '' }]);
  const [errors, setErrors] = useState({});
  const [confirmationChecked, setConfirmationChecked] = useState(false);

  // DEBUG: Console logging and alert
  console.log('🚨 PassengerDetailsSectionNew component rendered!');
  console.log('🚨 confirmationChecked state:', confirmationChecked);

  // Show alert on first render to confirm component is loading
  React.useEffect(() => {
    alert('🚨 NEW COMPONENT IS LOADING! Check the page for debug banners.');
  }, []);

  // Email validation
  const validateEmail = (email) => {
    if (!email.trim()) {
      return 'Email address is required';
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  };

  // Passenger validation
  const validatePassenger = (passenger, index) => {
    const errors = {};
    if (!passenger.firstName.trim()) {
      errors[`passenger_${index}_firstName`] = 'First name is required';
    }
    if (!passenger.lastName.trim()) {
      errors[`passenger_${index}_lastName`] = 'Last name is required';
    }
    return errors;
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};
    
    // Validate email
    const emailError = validateEmail(email);
    if (emailError) {
      newErrors.email = emailError;
    }
    
    // Validate passengers
    passengers.forEach((passenger, index) => {
      const passengerErrors = validatePassenger(passenger, index);
      Object.assign(newErrors, passengerErrors);
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Event handlers
  const handleEmailChange = (e) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    
    // Clear email error when user starts typing
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: null }));
    }
  };

  const handlePassengerChange = (index, field, value) => {
    const newPassengers = [...passengers];
    newPassengers[index][field] = value;
    setPassengers(newPassengers);
    
    // Clear specific passenger error when user starts typing
    const errorKey = `passenger_${index}_${field}`;
    if (errors[errorKey]) {
      setErrors(prev => ({ ...prev, [errorKey]: null }));
    }
  };

  const addPassenger = () => {
    if (passengers.length < 9) {
      setPassengers([...passengers, { firstName: '', lastName: '' }]);
    }
  };

  const removePassenger = (index) => {
    if (passengers.length > 1) {
      const newPassengers = passengers.filter((_, i) => i !== index);
      setPassengers(newPassengers);
      
      // Clear errors for removed passenger
      const newErrors = { ...errors };
      delete newErrors[`passenger_${index}_firstName`];
      delete newErrors[`passenger_${index}_lastName`];
      setErrors(newErrors);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // First check confirmation checkbox
    if (!confirmationChecked) {
      setErrors(prev => ({
        ...prev,
        confirmation: 'You must confirm that you have reviewed your details before proceeding'
      }));
      return;
    }

    // Then validate form
    if (!validateForm()) {
      return;
    }

    // Clear confirmation error if it exists
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.confirmation;
      return newErrors;
    });

    // Format data for submission
    const formData = {
      email: email.trim(),
      passengers: passengers.map(p => ({
        firstName: p.firstName.trim(),
        lastName: p.lastName.trim()
      }))
    };

    onSubmit(formData);
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg border border-gray-100 ${className}`}>
      <div className="p-6 sm:p-8">
        {/* DEBUG: Component Loading Test */}
        <div className="mb-4 p-8 bg-red-600 text-white text-center font-bold text-3xl border-4 border-yellow-400" style={{position: 'relative', zIndex: 9999}}>
          🚨🚨🚨 NEW COMPONENT LOADED 🚨🚨🚨
          <br />
          confirmationChecked: {confirmationChecked.toString()}
          <br />
          Time: {new Date().toLocaleTimeString()}
        </div>

        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Passenger Details
          </h2>
          <p className="text-gray-600">
            Please provide your contact information and passenger details
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Contact Information Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <EnvelopeIcon className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                Contact Information
              </h3>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={handleEmailChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
              {errors.email && (
                <div className="mt-2 flex items-center gap-2 text-red-600">
                  <ExclamationTriangleIcon className="w-4 h-4 flex-shrink-0" />
                  <span className="text-sm font-medium">{errors.email}</span>
                </div>
              )}
            </div>
          </div>

          {/* Passenger Information Section */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <UserIcon className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Passenger Information
                </h3>
              </div>
              {passengers.length < 9 && (
                <button
                  type="button"
                  onClick={addPassenger}
                  className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                  disabled={isLoading}
                >
                  <PlusIcon className="w-4 h-4" />
                  Add Passenger
                </button>
              )}
            </div>

            {passengers.map((passenger, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">
                    Passenger {index + 1}
                  </h4>
                  {passengers.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removePassenger(index)}
                      className="text-red-600 hover:text-red-700 p-1"
                      disabled={isLoading}
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      value={passenger.firstName}
                      onChange={(e) => handlePassengerChange(index, 'firstName', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors[`passenger_${index}_firstName`] ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder="First Name"
                      disabled={isLoading}
                    />
                    {errors[`passenger_${index}_firstName`] && (
                      <div className="mt-1 flex items-center gap-2 text-red-600">
                        <ExclamationTriangleIcon className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm">{errors[`passenger_${index}_firstName`]}</span>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      value={passenger.lastName}
                      onChange={(e) => handlePassengerChange(index, 'lastName', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors[`passenger_${index}_lastName`] ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder="Last Name"
                      disabled={isLoading}
                    />
                    {errors[`passenger_${index}_lastName`] && (
                      <div className="mt-1 flex items-center gap-2 text-red-600">
                        <ExclamationTriangleIcon className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm">{errors[`passenger_${index}_lastName`]}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {passengers.length >= 9 && (
              <p className="text-sm text-orange-600 text-center">
                Maximum of 9 passengers allowed
              </p>
            )}
          </div>

          {/* MANDATORY CONFIRMATION CHECKBOX - POSITIONED HERE FOR VISIBILITY */}
          <div className="border-t border-gray-200 pt-6">
            {/* DEBUG: Checkbox Section Marker */}
            <div className="mb-2 p-2 bg-green-500 text-white text-center font-bold">
              🟢 CHECKBOX SECTION RENDERING 🟢
            </div>

            <div className="mb-6 p-6 bg-red-100 border-4 border-red-500 rounded-lg" style={{position: 'relative', zIndex: 999}}>
              <div className="flex items-start space-x-4">
                <input
                  id="confirmation-checkbox"
                  type="checkbox"
                  checked={confirmationChecked}
                  onChange={(e) => {
                    console.log('🚨 Checkbox clicked!', e.target.checked);
                    setConfirmationChecked(e.target.checked);
                    // Clear confirmation error when checkbox is checked
                    if (e.target.checked && errors.confirmation) {
                      setErrors(prev => {
                        const newErrors = { ...prev };
                        delete newErrors.confirmation;
                        return newErrors;
                      });
                    }
                  }}
                  className="w-8 h-8 mt-1 text-blue-600 bg-white border-4 border-blue-500 rounded focus:ring-blue-500 focus:ring-4"
                  disabled={isLoading}
                  style={{minWidth: '32px', minHeight: '32px'}}
                />
                <label htmlFor="confirmation-checkbox" className="text-red-800 font-bold text-lg leading-relaxed cursor-pointer">
                  🚨 MANDATORY: Please double-check your flight details and passenger information before proceeding to payment.
                  <span className="font-black text-xl"> Changes cannot be made after this step.</span>
                </label>
              </div>

              {/* Confirmation Error */}
              {errors.confirmation && (
                <div className="mt-3 flex items-center gap-2 text-red-600">
                  <ExclamationTriangleIcon className="w-4 h-4 flex-shrink-0" />
                  <span className="text-sm font-medium">{errors.confirmation}</span>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={isLoading || !confirmationChecked}
              className="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Processing...
                </div>
              ) : (
                submitButtonText
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PassengerDetailsSectionNew;
