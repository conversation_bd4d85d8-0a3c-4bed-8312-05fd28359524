import React from 'react';

const FAQSchema = ({ content, postTitle, postUrl }) => {
  // Extract FAQ sections from content
  const extractFAQs = (content) => {
    const faqs = [];
    
    // Split content into lines for processing
    const lines = content.split('\n');
    let currentQuestion = null;
    let currentAnswer = [];
    let inFAQSection = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Detect FAQ sections
      if (line.toLowerCase().includes('faq') || line.toLowerCase().includes('frequently asked')) {
        inFAQSection = true;
        continue;
      }
      
      // Detect questions (starting with ### Q: or similar patterns)
      if (line.match(/^###\s*Q[:\s]/i) || line.match(/^###\s*Question[:\s]/i)) {
        // Save previous FAQ if exists
        if (currentQuestion && currentAnswer.length > 0) {
          faqs.push({
            question: currentQuestion.replace(/^###\s*Q[:\s]*/i, '').replace(/^###\s*Question[:\s]*/i, '').trim(),
            answer: currentAnswer.join(' ').replace(/^[✅❌]\s*/, '').trim()
          });
        }
        
        // Start new question
        currentQuestion = line;
        currentAnswer = [];
        continue;
      }
      
      // Collect answer lines
      if (currentQuestion && line) {
        // Skip empty lines and section headers
        if (!line.match(/^#{1,6}\s/) && line !== '') {
          currentAnswer.push(line);
        }
      }
      
      // If we hit a new section header that's not a question, save current FAQ
      if (line.match(/^##\s/) && !line.match(/^###\s*Q[:\s]/i) && currentQuestion) {
        if (currentAnswer.length > 0) {
          faqs.push({
            question: currentQuestion.replace(/^###\s*Q[:\s]*/i, '').replace(/^###\s*Question[:\s]*/i, '').trim(),
            answer: currentAnswer.join(' ').replace(/^[✅❌]\s*/, '').trim()
          });
        }
        currentQuestion = null;
        currentAnswer = [];
        inFAQSection = false;
      }
    }
    
    // Don't forget the last FAQ
    if (currentQuestion && currentAnswer.length > 0) {
      faqs.push({
        question: currentQuestion.replace(/^###\s*Q[:\s]*/i, '').replace(/^###\s*Question[:\s]*/i, '').trim(),
        answer: currentAnswer.join(' ').replace(/^[✅❌]\s*/, '').trim()
      });
    }
    
    return faqs;
  };

  const faqs = extractFAQs(content);
  
  // Don't render if no FAQs found
  if (faqs.length === 0) {
    return null;
  }

  // Generate FAQ Schema JSON-LD
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };

  // Also generate Article Schema for the blog post
  const articleSchema = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": postTitle,
    "url": postUrl,
    "datePublished": new Date().toISOString(),
    "dateModified": new Date().toISOString(),
    "author": {
      "@type": "Organization",
      "name": "VerifiedOnward",
      "url": "https://verifiedonward.com"
    },
    "publisher": {
      "@type": "Organization",
      "name": "VerifiedOnward",
      "url": "https://verifiedonward.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://verifiedonward.com/logo.png"
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": postUrl
    },
    "description": "Expert guide on visa applications and travel documentation requirements."
  };

  // Generate Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://verifiedonward.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Blog",
        "item": "https://verifiedonward.com/blog"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": postTitle,
        "item": postUrl
      }
    ]
  };

  return (
    <>
      {/* FAQ Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema, null, 2)
        }}
      />
      
      {/* Article Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema, null, 2)
        }}
      />
      
      {/* Breadcrumb Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema, null, 2)
        }}
      />
    </>
  );
};

export default FAQSchema;
