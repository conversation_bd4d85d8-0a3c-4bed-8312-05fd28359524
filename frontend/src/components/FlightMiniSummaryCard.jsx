import React from 'react';
import { motion } from 'framer-motion';

const FlightMiniSummaryCard = ({
  flight,
  title = "Flight",
  date = null,
  onEdit = () => {},
  className = ""
}) => {
  // Safety check for flight data
  if (!flight || typeof flight !== 'object') {
    console.warn('FlightMiniSummaryCard: Invalid flight data', flight);
    return null;
  }

  // Helper function to format time
  const formatTime = (dateString) => {
    if (!dateString) return '--:--';
    try {
      return new Date(dateString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch (error) {
      return '--:--';
    }
  };

  // Helper function to format duration
  const formatDuration = (duration) => {
    if (!duration) return '--';
    if (typeof duration === 'string') return duration;
    if (typeof duration === 'number') {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return `${hours}h ${minutes}m`;
    }
    return '--';
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return null;
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return null;
    }
  };

  // Extract flight data safely
  const airline = flight.airline || {};
  const flightInfo = flight.flight || {};
  const departure = flightInfo.departure || {};
  const arrival = flightInfo.arrival || {};

  const airlineName = airline.name || 'Unknown Airline';
  const flightNumber = flightInfo.number || '--';
  const departureTime = formatTime(departure.time);
  const arrivalTime = formatTime(arrival.time);
  const duration = formatDuration(flightInfo.duration);
  // Extract price from price object
  const priceObj = flight.price || {};
  console.log('🔍 FlightMiniSummaryCard price object:', priceObj);
  const originalPrice = priceObj.originalPrice || '0';
  const displayPrice = priceObj.displayPrice || priceObj.total || '4.99';

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={`bg-white border border-blue-200 rounded-lg shadow-sm overflow-hidden ${className}`}
    >
      <div className="p-4">
        {/* Header with title and edit button */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-blue-700 text-sm font-semibold">{title}</span>
          </div>
          <button
            onClick={onEdit}
            className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-xs font-medium transition-colors duration-200"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span>Edit</span>
          </button>
        </div>

        {/* Flight Details */}
        <div className="space-y-2">
          {/* Airline and Flight Number */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
              {airline.logo ? (
                <img
                  src={airline.logo}
                  alt={airlineName}
                  className="w-6 h-6 object-contain"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
              ) : null}
              <div className={`w-6 h-6 flex items-center justify-center text-xs font-bold text-gray-600 ${airline.logo ? 'hidden' : ''}`}>
                ✈️
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">{airlineName}</div>
              <div className="text-xs text-gray-500">{flightNumber}</div>
            </div>
          </div>

          {/* Time and Duration */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              <span className="font-medium">{departureTime} → {arrivalTime}</span>
            </div>
            <div className="text-xs text-gray-500">{duration}</div>
          </div>

          {/* Flight Date */}
          {date && (
            <div className="text-xs text-gray-500 mt-1">
              {formatDate(date)}
            </div>
          )}

          {/* Price */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <span className="text-xs text-gray-500">Price:</span>
            <div className="text-right">
              <div className="price-search-results">${displayPrice}</div>
              <div className="text-xs text-gray-400 line-through">${originalPrice}</div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default FlightMiniSummaryCard;
