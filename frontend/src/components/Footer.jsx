import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import MobileFooterAccordion from './MobileAccordion';

const Footer = () => {
  return (
    <motion.footer
      className="relative bg-gradient-to-br from-brand-900 via-brand-800 to-neutral-900 text-white overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5, duration: 0.5 }}
    >
      {/* Enhanced contrast overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-neutral-900/80 via-brand-900/70 to-neutral-900/80"></div>
      {/* Premium Aviation background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-500/10 via-transparent to-accent-500/10"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-brand-400/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-accent-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>

      {/* Subtle Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.03]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>
      <div className="container-modern section-padding relative z-10">
        {/* Mobile Accordion Footer */}
        <MobileFooterAccordion />

        {/* Desktop Footer Layout */}
        <div className="hidden lg:grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-0 lg:divide-x lg:divide-white/10">
          {/* Premium Company Info */}
          <div className="col-span-1 lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="lg:pr-8"
            >
              {/* Premium Aviation logo section */}
              <div className="flex items-center space-x-6 mb-10">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-brand-500 to-brand-600 rounded-3xl flex items-center justify-center shadow-aviation group-hover:shadow-aviation-hover transition-all duration-300 transform group-hover:scale-105">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                    </svg>
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  {/* Premium aviation glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-brand-500/40 to-brand-600/40 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div>
                  <h3 className="text-4xl font-black bg-gradient-to-r from-white via-brand-200 to-accent-200 bg-clip-text text-transparent">
                    VerifiedOnward
                  </h3>
                  <p className="text-brand-300 font-bold text-lg">Professional Flight Reservations</p>
                  <div className="flex items-center space-x-3 mt-3">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-300 font-bold">Trusted by 75,000+ Travelers Worldwide</span>
                  </div>
                </div>
              </div>

              <p className="text-neutral-100 leading-relaxed text-xl mb-8 font-semibold">
                Get professional airline reservations with verifiable booking references in 60 seconds. Professionally formatted and visa-ready
                flight documents that meet common visa application standards across 195+ countries.
              </p>

              {/* Premium Trust Badges - 20% smaller and horizontally aligned */}
              <div className="flex justify-center items-center gap-6">
                <div className="text-center p-2 bg-white/5 rounded-lg border border-white/10">
                  <div className="text-base font-bold text-green-400">99.7%</div>
                  <div className="text-xs text-green-300 font-medium">Visa Success</div>
                </div>
                <div className="text-center p-2 bg-white/5 rounded-lg border border-white/10">
                  <div className="text-base font-bold text-brand-400">75,000+</div>
                  <div className="text-xs text-brand-300 font-medium">Happy Travelers</div>
                </div>
                <div className="text-center p-2 bg-white/5 rounded-lg border border-white/10">
                  <div className="text-base font-bold text-accent-400">60s</div>
                  <div className="text-xs text-accent-300 font-medium">Instant Download</div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Premium Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="lg:pl-8"
          >
            <h4 className="text-xl font-bold mb-6 bg-gradient-to-r from-brand-300 to-accent-300 bg-clip-text text-transparent">
              Quick Links
            </h4>
            <ul className="space-y-3">
              {[
                { to: "/how-it-works", label: "How It Works", icon: "⚡" },
                { to: "/use-cases", label: "Use Cases", icon: "💼" },
                { to: "/faq", label: "FAQ", icon: "❓" },
                { to: "/blog", label: "Blog / Resources", icon: "📚" },
                { to: "/privacy-policy", label: "Privacy Policy", icon: "🔒" },
                { to: "/terms-of-service", label: "Terms of Service", icon: "📋" }
              ].map((link, index) => (
                <motion.li
                  key={link.to}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <Link
                    to={link.to}
                    className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-lg px-3 py-2 transition-all duration-300 group text-sm font-medium"
                  >
                    <span className="text-sm group-hover:scale-110 transition-transform duration-200">{link.icon}</span>
                    <span className="group-hover:translate-x-1 transition-transform duration-200">{link.label}</span>
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Premium Support */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:pl-8"
          >
            <h4 className="text-xl font-bold mb-6 bg-gradient-to-r from-accent-300 to-brand-300 bg-clip-text text-transparent">
              Support
            </h4>
            <ul className="space-y-3">
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Link
                  to="/contact"
                  className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-lg px-3 py-2 transition-all duration-300 group text-sm font-medium"
                >
                  <span className="text-sm group-hover:scale-110 transition-transform duration-200">📞</span>
                  <span className="group-hover:translate-x-1 transition-transform duration-200">Contact Us</span>
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.05 }}
              >
                <Link
                  to="/refund-policy"
                  className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-lg px-3 py-2 transition-all duration-300 group text-sm font-medium"
                >
                  <span className="text-sm group-hover:scale-110 transition-transform duration-200">📋</span>
                  <span className="group-hover:translate-x-1 transition-transform duration-200">Refund Policy</span>
                </Link>
              </motion.li>
            </ul>

            {/* Removed premium contact CTA button for cleaner footer */}
          </motion.div>
        </div>

        {/* Secure Payment Methods Section - Compact Layout */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-12 pt-8"
        >
          <div className="text-center mb-8">
            <p className="text-sm text-neutral-400 mb-4">Secure Payments Powered By</p>
            <div className="flex flex-wrap justify-center items-center gap-6 md:gap-8 py-4">
              {/* Stripe Logo */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="flex items-center space-x-2 bg-white/10 backdrop-blur-xl rounded-xl px-4 py-3 shadow-soft border border-white/20 transition-all duration-300 hover:bg-white/15 hover:brightness-110"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white shadow-soft">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold text-white">Stripe</span>
              </motion.div>

              {/* PayPal Logo */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 }}
                className="flex items-center space-x-2 bg-white/10 backdrop-blur-xl rounded-xl px-4 py-3 shadow-soft border border-white/20 transition-all duration-300 hover:bg-white/15 hover:brightness-110"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center text-white shadow-soft">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h8.418c2.508 0 4.514.893 5.535 2.459 1.008 1.552 1.055 3.736.135 6.17-.836 2.21-2.314 3.93-4.28 4.98-1.97 1.05-4.332 1.582-6.847 1.582H6.233a.862.862 0 0 0-.85.704l-.413 2.442zM6.585 10.898c.362 0 .656-.29.656-.65 0-.36-.294-.65-.656-.65-.363 0-.657.29-.657.65 0 .36.294.65.657.65z"/>
                  </svg>
                </div>
                <span className="text-sm font-semibold text-white">PayPal</span>
              </motion.div>

              {/* SSL Logo */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
                className="flex items-center space-x-2 bg-white/10 backdrop-blur-xl rounded-xl px-4 py-3 shadow-soft border border-white/20 transition-all duration-300 hover:bg-white/15 hover:brightness-110"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center text-white shadow-soft">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold text-white">SSL</span>
              </motion.div>

              {/* PCI Logo */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                className="flex items-center space-x-2 bg-white/10 backdrop-blur-xl rounded-xl px-4 py-3 shadow-soft border border-white/20 transition-all duration-300 hover:bg-white/15 hover:brightness-110"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center text-white shadow-soft">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold text-white">PCI</span>
              </motion.div>
            </div>
          </div>
        </motion.div>



        {/* Premium Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="border-t border-white/10 pt-4 mt-8"
        >
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-6">
              <p className="text-neutral-300 text-sm font-medium">
                © 2025 VerifiedOnward.com. All rights reserved.
              </p>
              <div className="flex items-center space-x-4 text-xs text-neutral-400 font-medium">
                <span className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                  <a href="mailto:<EMAIL>" className="hover:text-white transition-colors duration-200">
                    Email Support
                  </a>
                </span>
                <span>•</span>
                <span>99.7% Visa Acceptance Rate</span>
                <span>•</span>
                <span>Professional Quality Standards</span>
              </div>
            </div>

            {/* Premium social proof */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-neutral-400 text-sm">
                <span>⭐⭐⭐⭐⭐</span>
                <span>4.9/5 from 12,847 reviews</span>
              </div>
            </div>
          </div>


        </motion.div>
      </div>
    </motion.footer>
  );
};

export default Footer;
