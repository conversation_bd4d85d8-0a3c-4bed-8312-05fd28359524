import React from 'react';

const SimpleBlogRenderer = ({ post }) => {
  if (!post) {
    return (
      <div style={{ padding: '20px', background: 'yellow', color: 'black' }}>
        <h1>No Post Found</h1>
        <p>Post data is null or undefined</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto', background: 'white' }}>
      <div style={{ background: 'lightblue', padding: '10px', marginBottom: '20px' }}>
        <h1>SimpleBlogRenderer Test</h1>
        <p>Post Title: {post.title}</p>
        <p>Content Length: {post.content?.length || 0}</p>
      </div>
      
      <article>
        <header style={{ marginBottom: '30px' }}>
          <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '10px' }}>
            {post.title}
          </h1>
          <div style={{ color: '#666', marginBottom: '10px' }}>
            <span>Published: {post.publishDate}</span>
            <span style={{ marginLeft: '20px' }}>Reading Time: {post.readingTime}</span>
          </div>
          <p style={{ fontSize: '1.2rem', color: '#555', lineHeight: '1.6' }}>
            {post.excerpt}
          </p>
        </header>
        
        <div style={{ lineHeight: '1.8', fontSize: '1.1rem' }}>
          {/* Simple content rendering - just display raw content for now */}
          <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
            {post.content}
          </pre>
        </div>
      </article>
    </div>
  );
};

export default SimpleBlogRenderer;
