import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';
import {
  CheckCircleIcon,
  ShieldCheckIcon,
  ClockIcon,
  DocumentCheckIcon,
  GlobeAltIcon,
  ArrowRightIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ExclamationTriangleIcon,
  StarIcon,
  LightBulbIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

// Hybrid SEO Blog Template - Optimized for Google + AI Search Engines
const HybridSEOBlogTemplate = ({ post }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedFAQs, setExpandedFAQs] = useState({});

  // Parse structured content from post
  const parseStructuredContent = (content) => {
    const sections = content.split(/(?=##\s)/);
    const parsedSections = [];
    
    sections.forEach((section, index) => {
      if (section.trim()) {
        const lines = section.trim().split('\n');
        const title = lines[0].replace(/^##\s*/, '');
        const content = lines.slice(1).join('\n').trim();
        
        parsedSections.push({
          id: `section-${index}`,
          title,
          content,
          type: detectSectionType(title)
        });
      }
    });
    
    return parsedSections;
  };

  const detectSectionType = (title) => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('faq') || lowerTitle.includes('question')) return 'faq';
    if (lowerTitle.includes('step') || lowerTitle.includes('guide')) return 'steps';
    if (lowerTitle.includes('mistake') || lowerTitle.includes('avoid')) return 'warnings';
    if (lowerTitle.includes('tip') || lowerTitle.includes('expert')) return 'tips';
    return 'content';
  };

  const parseFAQs = (content) => {
    const faqRegex = /### Q: (.+?)\n✅ (.+?)(?=\n### Q:|$)/gs;
    const faqs = [];
    let match;
    
    while ((match = faqRegex.exec(content)) !== null) {
      faqs.push({
        question: match[1].trim(),
        answer: match[2].trim()
      });
    }
    
    return faqs;
  };

  const sections = parseStructuredContent(post.content);
  const faqs = parseFAQs(post.content);

  // Generate comprehensive structured data
  const generateStructuredData = () => {
    const baseStructuredData = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": post.title,
      "description": post.excerpt,
      "author": {
        "@type": "Person",
        "name": "VerifiedOnward Travel Expert",
        "url": "https://verifiedonward.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "VerifiedOnward",
        "logo": {
          "@type": "ImageObject",
          "url": "https://verifiedonward.com/logo.png"
        }
      },
      "datePublished": post.publishDate,
      "dateModified": post.publishDate,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `https://verifiedonward.com/blog/${post.slug}`
      },
      "keywords": post.metaKeywords?.join(', ') || '',
      "articleSection": "Travel Guides",
      "wordCount": post.content.length
    };

    // Add FAQ structured data if FAQs exist
    if (faqs.length > 0) {
      const faqStructuredData = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": faqs.map(faq => ({
          "@type": "Question",
          "name": faq.question,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": faq.answer
          }
        }))
      };

      return [baseStructuredData, faqStructuredData];
    }

    return [baseStructuredData];
  };

  // SEO Meta tags
  useEffect(() => {
    // Update document title
    document.title = post.metaTitle || `${post.title} | VerifiedOnward`;
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', post.metaDescription || post.excerpt);
    }

    // Add structured data
    const structuredDataArray = generateStructuredData();
    structuredDataArray.forEach((data, index) => {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.text = JSON.stringify(data);
      script.id = `structured-data-${index}`;
      document.head.appendChild(script);
    });

    // Cleanup on unmount
    return () => {
      structuredDataArray.forEach((_, index) => {
        const script = document.getElementById(`structured-data-${index}`);
        if (script) script.remove();
      });
    };
  }, [post]);

  const toggleFAQ = (index) => {
    setExpandedFAQs(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Hero Section with SEO-optimized structure
  const HeroSection = () => (
    <section className="relative py-16 lg:py-24 bg-gradient-to-br from-brand-50 via-white to-accent-50/30 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>
      
      <div className="container-modern relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto text-center"
        >
          {/* SEO-optimized H1 with primary keyword */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-black text-neutral-900 mb-6 leading-tight">
            {post.title}
          </h1>

          {/* Expert authority signals */}
          <div className="flex items-center justify-center space-x-6 mb-8">
            <div className="flex items-center text-brand-600">
              <ShieldCheckIcon className="w-5 h-5 mr-2" />
              <span className="text-sm font-semibold">Expert Guide</span>
            </div>
            <div className="flex items-center text-green-600">
              <CheckCircleIcon className="w-5 h-5 mr-2" />
              <span className="text-sm font-semibold">Embassy Approved</span>
            </div>
            <div className="flex items-center text-accent-600">
              <StarIcon className="w-5 h-5 mr-2" />
              <span className="text-sm font-semibold">Trusted by 75,000+</span>
            </div>
          </div>

          {/* SEO-optimized excerpt with primary keyword in first 100 words */}
          <p className="text-xl md:text-2xl text-neutral-600 mb-8 leading-relaxed font-medium max-w-3xl mx-auto">
            {post.excerpt}
          </p>

          {/* Article metadata for AI parsing */}
          <div className="flex items-center justify-center space-x-6 text-neutral-500 text-sm">
            <div className="flex items-center">
              <ClockIcon className="w-4 h-4 mr-2" />
              <span>{post.readTime}</span>
            </div>
            <div className="flex items-center">
              <DocumentCheckIcon className="w-4 h-4 mr-2" />
              <span>Updated for 2025</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );

  // Quick Summary Section (AI-friendly)
  const QuickSummary = () => (
    <section className="py-12 bg-brand-50/30">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-white rounded-2xl p-8 shadow-soft border border-brand-100">
            <div className="flex items-center mb-4">
              <InformationCircleIcon className="w-6 h-6 text-brand-600 mr-3" />
              <h2 className="text-xl font-bold text-neutral-900">Quick Summary</h2>
            </div>
            <div className="prose prose-lg max-w-none">
              <p className="text-neutral-700 leading-relaxed mb-0">
                {post.excerpt}
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );

  // Main Content Sections with proper H2/H3 hierarchy
  const MainContent = () => (
    <section className="py-16">
      <div className="container-modern">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Main Article Content */}
            <article className="lg:col-span-3">
              <div className="prose prose-lg max-w-none">
                {sections.map((section, index) => (
                  <motion.div
                    key={section.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="mb-12"
                  >
                    {renderSection(section)}
                  </motion.div>
                ))}
              </div>
            </article>

            {/* Sidebar with Table of Contents */}
            <aside className="lg:col-span-1">
              <div className="sticky top-8">
                <TableOfContents sections={sections} />
              </div>
            </aside>
          </div>
        </div>
      </div>
    </section>
  );

  // Render different section types with appropriate styling
  const renderSection = (section) => {
    const getSectionIcon = (type) => {
      switch (type) {
        case 'steps': return <DocumentCheckIcon className="w-6 h-6" />;
        case 'warnings': return <ExclamationTriangleIcon className="w-6 h-6" />;
        case 'tips': return <LightBulbIcon className="w-6 h-6" />;
        case 'faq': return <InformationCircleIcon className="w-6 h-6" />;
        default: return <GlobeAltIcon className="w-6 h-6" />;
      }
    };

    const getSectionColor = (type) => {
      switch (type) {
        case 'steps': return 'brand';
        case 'warnings': return 'red';
        case 'tips': return 'green';
        case 'faq': return 'blue';
        default: return 'neutral';
      }
    };

    const color = getSectionColor(section.type);

    return (
      <div className={`bg-${color}-50/50 rounded-2xl p-8 border border-${color}-100`}>
        <div className="flex items-center mb-6">
          <div className={`text-${color}-600 mr-3`}>
            {getSectionIcon(section.type)}
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-neutral-900">
            {section.title}
          </h2>
        </div>

        <div className="prose prose-lg max-w-none">
          {renderSectionContent(section.content, section.type)}
        </div>
      </div>
    );
  };

  // Render section content with proper formatting
  const renderSectionContent = (content, type) => {
    const lines = content.split('\n').filter(line => line.trim());

    return lines.map((line, index) => {
      // Handle step-by-step content
      if (type === 'steps' && line.match(/^### \d+️⃣/)) {
        const stepTitle = line.replace(/^### /, '');
        return (
          <h3 key={index} className="text-xl font-bold text-neutral-800 mb-4 flex items-center">
            {stepTitle}
          </h3>
        );
      }

      // Handle bullet points
      if (line.startsWith('✅') || line.startsWith('❌')) {
        const isPositive = line.startsWith('✅');
        return (
          <div key={index} className={`flex items-start mb-3 p-3 rounded-lg ${isPositive ? 'bg-green-50' : 'bg-red-50'}`}>
            <span className={`mr-3 text-lg ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {line.charAt(0)}
            </span>
            <span className="text-neutral-700">{line.slice(2)}</span>
          </div>
        );
      }

      // Handle bold text
      if (line.startsWith('**') && line.endsWith('**')) {
        return (
          <p key={index} className="font-bold text-neutral-800 mb-4">
            {line.replace(/\*\*/g, '')}
          </p>
        );
      }

      // Regular paragraph
      return (
        <p key={index} className="text-neutral-700 leading-relaxed mb-4">
          {line}
        </p>
      );
    });
  };

  // Table of Contents for better navigation
  const TableOfContents = ({ sections }) => (
    <div className="bg-white rounded-2xl p-6 shadow-soft border border-neutral-100">
      <h3 className="text-lg font-bold text-neutral-900 mb-4">Table of Contents</h3>
      <nav>
        <ul className="space-y-2">
          {sections.map((section, index) => (
            <li key={section.id}>
              <a
                href={`#${section.id}`}
                className="text-sm text-neutral-600 hover:text-brand-600 transition-colors duration-200 block py-1"
              >
                {section.title}
              </a>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );

  // FAQ Section with structured data
  const FAQSection = () => {
    if (faqs.length === 0) return null;

    return (
      <section className="py-16 bg-neutral-50">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-lg text-neutral-600">
                Quick answers to common questions about flight reservations
              </p>
            </div>

            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="bg-white rounded-2xl shadow-soft border border-neutral-100 overflow-hidden"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors duration-200"
                  >
                    <h3 className="text-lg font-semibold text-neutral-900 pr-4">
                      {faq.question}
                    </h3>
                    {expandedFAQs[index] ? (
                      <ChevronUpIcon className="w-5 h-5 text-neutral-500 flex-shrink-0" />
                    ) : (
                      <ChevronDownIcon className="w-5 h-5 text-neutral-500 flex-shrink-0" />
                    )}
                  </button>

                  {expandedFAQs[index] && (
                    <div className="px-6 pb-4">
                      <p className="text-neutral-700 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    );
  };

  // Expert Tips Section
  const ExpertTipsSection = () => (
    <section className="py-16 bg-gradient-to-br from-green-50 to-emerald-50">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <LightBulbIcon className="w-8 h-8 text-green-600 mr-3" />
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900">
                Expert Tips for Success
              </h2>
            </div>
            <p className="text-lg text-neutral-600">
              Professional insights from travel experts
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                icon: <CheckCircleIcon className="w-6 h-6" />,
                title: "Double-Check Details",
                tip: "Ensure your name matches your passport exactly. Even small differences can cause delays."
              },
              {
                icon: <ClockIcon className="w-6 h-6" />,
                title: "Apply Early",
                tip: "Submit your visa application at least 4-6 weeks before your intended travel date."
              },
              {
                icon: <DocumentCheckIcon className="w-6 h-6" />,
                title: "Keep Records",
                tip: "Save all confirmation emails and booking references for your visa interview."
              },
              {
                icon: <ShieldCheckIcon className="w-6 h-6" />,
                title: "Use Trusted Services",
                tip: "Only use visa-ready flight reservation services with proven track records."
              }
            ].map((tip, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-soft border border-green-100"
              >
                <div className="flex items-start">
                  <div className="text-green-600 mr-4 mt-1">
                    {tip.icon}
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-neutral-900 mb-2">
                      {tip.title}
                    </h3>
                    <p className="text-neutral-700 leading-relaxed">
                      {tip.tip}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );

  // Strategic CTA Section (minimal conversion pressure)
  const StrategicCTA = () => {
    const ctaProps = getCTANavigationProps(navigate, location);

    return (
      <section className="py-16 bg-gradient-to-br from-brand-50 to-accent-50/30">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-3xl mx-auto text-center"
          >
            <div className="bg-white rounded-3xl p-8 shadow-luxury border border-brand-100">
              <div className="flex items-center justify-center mb-6">
                <div className="bg-brand-100 rounded-full p-3">
                  <DocumentCheckIcon className="w-8 h-8 text-brand-600" />
                </div>
              </div>

              <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 mb-4">
                Ready to Get Your Embassy-Approved Flight Reservation?
              </h2>

              <p className="text-lg text-neutral-600 mb-8 leading-relaxed">
                Join 75,000+ travelers who trust VerifiedOnward for their visa applications.
                Get your professional flight reservation in under 60 seconds.
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-6">
                <div className="flex items-center text-green-600">
                  <CheckCircleIcon className="w-5 h-5 mr-2" />
                  <span className="text-sm font-semibold">99.8% Embassy Acceptance</span>
                </div>
                <div className="flex items-center text-brand-600">
                  <ClockIcon className="w-5 h-5 mr-2" />
                  <span className="text-sm font-semibold">Instant Delivery</span>
                </div>
                <div className="flex items-center text-accent-600">
                  <ShieldCheckIcon className="w-5 h-5 mr-2" />
                  <span className="text-sm font-semibold">Secure & Trusted</span>
                </div>
              </div>

              <Link
                {...ctaProps}
                className="inline-flex items-center bg-brand-600 hover:bg-brand-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:shadow-brand-glow hover:-translate-y-1"
              >
                Start Your Reservation
                <ArrowRightIcon className="w-5 h-5 ml-2" />
              </Link>

              <p className="text-xs text-neutral-500 mt-4">
                Starting from $4.99 • No hidden fees • Instant download
              </p>
            </div>
          </motion.div>
        </div>
      </section>
    );
  };

  // Conclusion Section
  const ConclusionSection = () => (
    <section className="py-16">
      <div className="container-modern">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-neutral-50 rounded-3xl p-8 border border-neutral-100">
            <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 mb-6 text-center">
              Key Takeaways
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-neutral-700">
                    <strong>Flight reservations</strong> are visa-ready and safer than buying actual tickets
                  </p>
                </div>
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-neutral-700">
                    <strong>Professional services</strong> provide properly formatted documents that visa offices recognize
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-neutral-700">
                    <strong>Accuracy is crucial</strong> - ensure all details match your passport exactly
                  </p>
                </div>
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-neutral-700">
                    <strong>Apply early</strong> to avoid last-minute stress and potential delays
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );

  return (
    <div className="min-h-screen bg-white">
      <HeroSection />
      <QuickSummary />
      <MainContent />
      <ExpertTipsSection />
      <FAQSection />
      <StrategicCTA />
      <ConclusionSection />
    </div>
  );
};

export default HybridSEOBlogTemplate;
