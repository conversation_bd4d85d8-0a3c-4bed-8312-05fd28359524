import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';
import { ArrowRightIcon, ClockIcon } from '@heroicons/react/24/outline';

const MinimalBlogSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 0.5 }}
      className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block"
    >
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-luxury border border-brand-100/50 p-6 max-w-xs">
        {/* Subtle Badge */}
        <div className="text-center mb-4">
          <div className="inline-flex items-center bg-brand-50 text-brand-700 px-4 py-2 rounded-full text-sm font-semibold mb-3">
            <ClockIcon className="w-4 h-4 mr-2" />
            Visa-Ready in 60s
          </div>
          
          <p className="text-sm text-neutral-600 leading-relaxed mb-4">
            Get your visa-ready flight reservation instantly
          </p>
        </div>

        {/* Soft CTA Button */}
        <Link
          {...getCTANavigationProps(navigate, location.pathname)}
          className="block w-full text-center px-6 py-3 bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-soft hover:shadow-brand-glow transform hover:scale-105 group"
        >
          <span className="flex items-center justify-center">
            Get Yours
            <ArrowRightIcon className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
          </span>
        </Link>

        {/* Trust Indicator */}
        <div className="mt-3 text-center">
          <p className="text-xs text-neutral-500">
            ✓ 75,000+ travelers trust us
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default MinimalBlogSidebar;
