import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';
import MarkdownRenderer from './MarkdownRenderer';

const ProfessionalBlogArticle = ({ post }) => {
  const navigate = useNavigate();
  const location = useLocation();



  if (!post) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-neutral-800 mb-4">Article not found</h1>
          <Link to="/blog" className="text-brand-600 hover:text-brand-700 font-medium">
            ← Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  // Parse FAQs from content for structured data
  const parseFAQs = (content) => {
    if (!content) return [];

    const lines = content.split('\n');
    const faqs = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.startsWith('### Q:') || line.startsWith('**Q:')) {
        const question = line.replace(/^### Q:\s*|^\*\*Q:\s*|\*\*$/g, '');
        // Look for the answer in the next few lines
        for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
          const answerLine = lines[j].trim();
          if (answerLine.startsWith('✅') || answerLine.startsWith('**A:') || answerLine.startsWith('A:')) {
            const answer = answerLine.replace(/^✅\s*|^\*\*A:\s*|\*\*$|^A:\s*/g, '');
            if (answer) {
              faqs.push({ question, answer });
              break;
            }
          }
        }
      }
    }

    return faqs;
  };

  const faqs = parseFAQs(post.content);

  // Generate structured data for SEO
  const generateStructuredData = () => {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": post.title,
      "description": post.excerpt,
      "author": {
        "@type": "Person",
        "name": "VerifiedOnward Travel Expert",
        "url": "https://verifiedonward.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "VerifiedOnward",
        "logo": {
          "@type": "ImageObject",
          "url": "https://verifiedonward.com/logo.png"
        }
      },
      "datePublished": post.publishDate,
      "dateModified": post.publishDate,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `https://verifiedonward.com/blog/${post.slug}`
      }
    };

    if (faqs.length > 0) {
      structuredData.mainEntity = {
        "@type": "FAQPage",
        "mainEntity": faqs.map(faq => ({
          "@type": "Question",
          "name": faq.question,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": faq.answer
          }
        }))
      };
    }

    return structuredData;
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(generateStructuredData()) }}
      />

      <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/20">
        {/* Article Header */}
        <header className="relative py-16 overflow-hidden">
          {/* Subtle Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-brand-500/3 via-blue-500/3 to-accent-500/3"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto">
              {/* Breadcrumbs */}
              <nav className="mb-8">
                <div className="flex items-center text-sm text-neutral-500">
                  <Link to="/" className="hover:text-brand-600 transition-colors">Home</Link>
                  <span className="mx-2">→</span>
                  <Link to="/blog" className="hover:text-brand-600 transition-colors">Blog</Link>
                  <span className="mx-2">→</span>
                  <span className="text-neutral-700 font-medium">Current Article</span>
                </div>
              </nav>

              {/* Article Title */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-black text-brand-800 mb-6 leading-tight">
                  {post.title.split(' ').map((word, index) => {
                    // Highlight key words with gradient
                    const keyWords = ['Dummy', 'Ticket', 'Visa', 'Embassy', 'Flight', 'Schengen', 'Guide'];
                    if (keyWords.some(key => word.toLowerCase().includes(key.toLowerCase()))) {
                      return (
                        <span key={index} className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">
                          {word}{' '}
                        </span>
                      );
                    }
                    return word + ' ';
                  })}
                </h1>

              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-neutral-600">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>{new Date(post.publishDate).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</span>
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2 text-accent-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{post.readTime}</span>
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-green-600 font-medium">Expert Guide</span>
                </div>
              </div>

              {/* Intro Summary */}
              <p className="text-lg md:text-xl text-neutral-700 leading-relaxed max-w-3xl">
                {post.excerpt}
              </p>
            </motion.div>
          </div>
        </div>
      </header>

        {/* Article Content */}
        <main className="py-12">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
                {/* Main Content */}
                <article className="lg:col-span-3">
                  <div className="max-w-none" style={{ maxWidth: '800px' }}>
                    {/* Introduction */}
                    <div className="mb-12">
                      <p className="text-lg text-neutral-700 leading-relaxed mb-6" style={{ fontSize: '18px', lineHeight: '1.7' }}>
                        {post.excerpt}
                      </p>
                    </div>

                    {/* Article Content with Markdown Rendering */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6 }}
                      className="mb-12"
                    >
                      <MarkdownRenderer content={post.content} />

                      {/* Mid-Article CTA - Insert after first few paragraphs */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="my-12 p-6 bg-gradient-to-r from-brand-50/50 to-blue-50/30 rounded-xl border border-brand-100/50"
                        style={{ borderRadius: '12px', marginTop: '48px', marginBottom: '48px' }}
                      >
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <span className="text-2xl">✈️</span>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-lg font-semibold text-brand-800 mb-2">
                              Pro Tip: Need a dummy ticket fast?
                            </h4>
                            <p className="text-neutral-700 mb-4 leading-relaxed">
                              Our service generates embassy-approved reservations in 60 seconds.
                            </p>
                            <Link
                              {...getCTANavigationProps(navigate, location.pathname)}
                              className="inline-flex items-center text-brand-600 hover:text-brand-700 font-medium text-sm group"
                            >
                              Learn More →
                              <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </Link>
                          </div>
                        </div>
                      </motion.div>
                    </motion.div>

                    {/* FAQ Section */}
                    {faqs.length > 0 && (
                      <motion.section
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="mb-12"
                        style={{ marginBottom: '48px' }}
                      >
                        <h2 className="text-2xl md:text-3xl font-bold text-neutral-800 mb-8 leading-tight">
                          Frequently Asked Questions
                        </h2>
                        <div className="space-y-6">
                          {faqs.map((faq, index) => (
                            <div key={index} className="bg-white rounded-xl p-6 shadow-soft border border-neutral-100">
                              <h3 className="text-lg font-semibold text-neutral-800 mb-3">
                                Q: {faq.question}
                              </h3>
                              <p className="text-neutral-700 leading-relaxed" style={{ fontSize: '18px', lineHeight: '1.7' }}>
                                ✅ {faq.answer}
                              </p>
                            </div>
                          ))}
                        </div>
                      </motion.section>
                    )}
                  </div>

                  {/* Trust Section */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="mt-12 p-6 bg-white rounded-xl shadow-soft border border-neutral-100"
                    style={{ marginTop: '48px', borderRadius: '12px' }}
                  >
                    <div className="flex flex-wrap justify-center gap-8 text-sm">
                      <div className="flex items-center text-neutral-600">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <div className="font-semibold text-neutral-800">75,000+</div>
                          <div className="text-xs text-neutral-500">Travelers Served</div>
                        </div>
                      </div>
                      <div className="flex items-center text-neutral-600">
                        <div className="w-8 h-8 bg-brand-100 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <div className="font-semibold text-neutral-800">99.7%</div>
                          <div className="text-xs text-neutral-500">Embassy Acceptance</div>
                        </div>
                      </div>
                      <div className="flex items-center text-neutral-600">
                        <div className="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-accent-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <div className="font-semibold text-neutral-800">60 Seconds</div>
                          <div className="text-xs text-neutral-500">Instant Delivery</div>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Author Bio */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="mt-12 p-6 bg-gradient-to-r from-neutral-50 to-brand-50/30 rounded-xl border border-neutral-100"
                    style={{ marginTop: '48px', borderRadius: '12px' }}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                        V
                      </div>
                      <div>
                        <h4 className="font-semibold text-neutral-800 mb-1">VerifiedOnward Travel Expert</h4>
                        <p className="text-sm text-neutral-600 leading-relaxed">
                          Our team of visa specialists has helped over 75,000 travelers secure their visas with professional flight reservations.
                          We stay updated on embassy requirements worldwide to ensure your application success.
                        </p>
                      </div>
                    </div>
                  </motion.div>

                  {/* Final CTA */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="mt-12 p-8 bg-gradient-to-br from-brand-50/50 to-blue-50/30 rounded-2xl border border-brand-100/50 text-center"
                    style={{ marginTop: '48px', borderRadius: '12px' }}
                  >
                    <h3 className="text-2xl md:text-3xl font-bold text-brand-800 mb-4">
                      ✅ Ready to apply for your visa?
                    </h3>
                    <p className="text-lg text-brand-700 mb-6 leading-relaxed max-w-2xl mx-auto">
                      Get your visa-ready flight reservation instantly, trusted by 75,000+ travelers.
                    </p>
                    <Link
                      {...getCTANavigationProps(navigate, location.pathname)}
                      className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-soft hover:shadow-brand-glow transform hover:scale-105 group"
                      style={{ borderRadius: '8px' }}
                    >
                      <span>Generate Your Ticket Now →</span>
                      <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                    <div className="mt-4 text-sm text-brand-600">
                      <p>✓ Secure payment • ✓ Instant download • ✓ Email support</p>
                    </div>
                  </motion.div>
                </article>

                {/* Sidebar */}
                <aside className="lg:col-span-1">
                  <div className="sticky top-8 space-y-6">
                    {/* Table of Contents */}
                    {sections.length > 0 && (
                      <div className="bg-white rounded-xl shadow-soft border border-neutral-100 p-6" style={{ borderRadius: '12px' }}>
                        <h4 className="text-lg font-semibold text-neutral-800 mb-4">Table of Contents</h4>
                        <nav className="space-y-2">
                          {sections.slice(0, 5).map((section, index) => (
                            <a
                              key={index}
                              href={`#section-${index}`}
                              className="block text-sm text-neutral-600 hover:text-brand-600 transition-colors py-1"
                            >
                              {section.title}
                            </a>
                          ))}
                        </nav>
                      </div>
                    )}

                    {/* Quick Links */}
                    <div className="bg-white rounded-xl shadow-soft border border-neutral-100 p-6" style={{ borderRadius: '12px' }}>
                      <h4 className="text-lg font-semibold text-neutral-800 mb-4">Related Guides</h4>
                      <nav className="space-y-3">
                        <Link to="/blog" className="block text-sm text-neutral-600 hover:text-brand-600 transition-colors py-1">
                          📋 All Visa Guides
                        </Link>
                        <Link to="/blog" className="block text-sm text-neutral-600 hover:text-brand-600 transition-colors py-1">
                          🏛️ Embassy Requirements
                        </Link>
                        <Link to="/blog" className="block text-sm text-neutral-600 hover:text-brand-600 transition-colors py-1">
                          💡 Application Tips
                        </Link>
                        <Link to="/blog" className="block text-sm text-neutral-600 hover:text-brand-600 transition-colors py-1">
                          ⚠️ Common Mistakes
                        </Link>
                      </nav>
                    </div>

                    {/* Trust Badges */}
                    <div className="bg-white rounded-xl shadow-soft border border-neutral-100 p-6" style={{ borderRadius: '12px' }}>
                      <h4 className="text-lg font-semibold text-neutral-800 mb-4">Why Choose Us</h4>
                      <div className="space-y-4">
                        <div className="flex items-center text-sm">
                          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-neutral-700 font-medium">Embassy Approved</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <div className="w-6 h-6 bg-brand-100 rounded-full flex items-center justify-center mr-3">
                            <svg className="w-3 h-3 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-neutral-700 font-medium">99.7% Success Rate</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <div className="w-6 h-6 bg-accent-100 rounded-full flex items-center justify-center mr-3">
                            <svg className="w-3 h-3 text-accent-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-neutral-700 font-medium">60s Delivery</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </aside>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
};

export default ProfessionalBlogArticle;
