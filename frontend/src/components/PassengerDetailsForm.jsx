import React, { useState } from 'react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

const PassengerDetailsForm = ({
  onSubmit,
  isLoading = false,
  submitButtonText = 'Continue to Payment'
}) => {
  // State management - all fields start empty
  const [email, setEmail] = useState('');
  const [passengers, setPassengers] = useState([
    { firstName: '', lastName: '' }
  ]);
  const [errors, setErrors] = useState({});
  const [confirmationChecked, setConfirmationChecked] = useState(false);

  // Add passenger handler
  const handleAddPassenger = () => {
    if (passengers.length < 2) {
      setPassengers([...passengers, { firstName: '', lastName: '' }]);
    }
  };

  // Remove passenger handler
  const handleRemovePassenger = (indexToRemove) => {
    if (passengers.length > 1) {
      const updatedPassengers = passengers.filter((_, index) => index !== indexToRemove);
      setPassengers(updatedPassengers);
      
      // Clear errors for removed passenger
      const newErrors = { ...errors };
      delete newErrors[`firstName_${indexToRemove}`];
      delete newErrors[`lastName_${indexToRemove}`];
      setErrors(newErrors);
    }
  };

  // Update passenger data
  const handlePassengerChange = (index, field, value) => {
    const updatedPassengers = [...passengers];
    updatedPassengers[index][field] = value;
    setPassengers(updatedPassengers);
    
    // Clear error when user starts typing
    if (errors[`${field}_${index}`]) {
      const newErrors = { ...errors };
      delete newErrors[`${field}_${index}`];
      setErrors(newErrors);
    }
  };

  // Email change handler
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    
    // Clear email error when user starts typing
    if (errors.email) {
      const newErrors = { ...errors };
      delete newErrors.email;
      setErrors(newErrors);
    }
  };

  // Validation function
  const validateForm = () => {
    const newErrors = {};
    
    // Validate email
    if (!email.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    // Validate passengers
    passengers.forEach((passenger, index) => {
      if (!passenger.firstName.trim()) {
        newErrors[`firstName_${index}`] = 'First name is required';
      }
      if (!passenger.lastName.trim()) {
        newErrors[`lastName_${index}`] = 'Last name is required';
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit handler
  const handleSubmit = (e) => {
    e.preventDefault();

    // First check confirmation checkbox
    if (!confirmationChecked) {
      setErrors(prev => ({
        ...prev,
        confirmation: 'You must confirm that you have reviewed your details before proceeding'
      }));
      return;
    }

    // Then validate form
    if (!validateForm()) {
      return;
    }

    // Clear confirmation error if it exists
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.confirmation;
      return newErrors;
    });

    // Format data for submission
    const formattedPassengers = passengers.map((passenger, index) => ({
      id: index + 1,
      firstName: passenger.firstName.trim(),
      lastName: passenger.lastName.trim()
    }));

    const formData = {
      passengers: formattedPassengers,
      email: email.trim()
    };

    console.log('✅ PassengerDetailsForm: Submitting data:', formData);

    if (onSubmit) {
      onSubmit(formData);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Passenger Details
        </h2>
        <p className="text-gray-600">
          Please provide your contact information and passenger details
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Contact Information Section */}
        <div>
          <h3 className="text-lg font-bold text-gray-900 mb-4">
            Contact Information
          </h3>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={handleEmailChange}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
              disabled={isLoading}
            />
            {errors.email && (
              <p className="mt-2 text-sm text-red-600">{errors.email}</p>
            )}
          </div>
        </div>

        {/* Passenger Information Section */}
        <div>
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-bold text-gray-900">
              Passenger Information
            </h3>
            
            {passengers.length < 2 && (
              <button
                type="button"
                onClick={handleAddPassenger}
                disabled={isLoading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Passenger
              </button>
            )}
          </div>

          {/* Passenger List */}
          <div className="space-y-6">
            {passengers.map((passenger, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium text-gray-900">
                    Passenger {index + 1}
                  </h4>
                  
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => handleRemovePassenger(index)}
                      disabled={isLoading}
                      className="text-red-600 hover:text-red-800 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Remove
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* First Name */}
                  <div>
                    <label 
                      htmlFor={`firstName_${index}`}
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      First Name *
                    </label>
                    <input
                      type="text"
                      id={`firstName_${index}`}
                      value={passenger.firstName}
                      onChange={(e) => handlePassengerChange(index, 'firstName', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors[`firstName_${index}`] ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="First Name"
                      disabled={isLoading}
                    />
                    {errors[`firstName_${index}`] && (
                      <p className="mt-2 text-sm text-red-600">{errors[`firstName_${index}`]}</p>
                    )}
                  </div>

                  {/* Last Name */}
                  <div>
                    <label 
                      htmlFor={`lastName_${index}`}
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id={`lastName_${index}`}
                      value={passenger.lastName}
                      onChange={(e) => handlePassengerChange(index, 'lastName', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors[`lastName_${index}`] ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Last Name"
                      disabled={isLoading}
                    />
                    {errors[`lastName_${index}`] && (
                      <p className="mt-2 text-sm text-red-600">{errors[`lastName_${index}`]}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Max passengers message */}
          {passengers.length >= 2 && (
            <div className="mt-4 text-center">
              <p className="text-sm text-amber-600 font-medium">
                Maximum of 2 passengers allowed
              </p>
            </div>
          )}
        </div>

        {/* Confirmation Checkbox */}
        <div className="pt-6 border-t border-gray-200">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 pt-0.5">
                <input
                  type="checkbox"
                  id="confirmation-checkbox"
                  checked={confirmationChecked}
                  onChange={(e) => setConfirmationChecked(e.target.checked)}
                  className="w-5 h-5 text-blue-600 bg-white border-2 border-blue-300 rounded focus:ring-blue-500 focus:ring-2 focus:ring-offset-2 transition-all duration-200"
                />
              </div>
              <div className="flex-1">
                <label htmlFor="confirmation-checkbox" className="text-blue-700 font-medium leading-relaxed cursor-pointer">
                  Please double-check your flight details and passenger information before proceeding to payment.
                  <span className="font-semibold text-blue-800"> Changes cannot be made after this step.</span>
                </label>
              </div>
            </div>

            {/* Confirmation Error */}
            {errors.confirmation && (
              <div className="mt-2 flex items-center gap-2 text-red-600">
                <ExclamationTriangleIcon className="w-4 h-4 flex-shrink-0" />
                <span className="text-sm font-medium">{errors.confirmation}</span>
              </div>
            )}
          </div>

          {/* Continue to Payment Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Processing...
              </div>
            ) : (
              submitButtonText
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PassengerDetailsForm;
