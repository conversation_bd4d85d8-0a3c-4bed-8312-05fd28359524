import React, { useEffect } from 'react';
import <PERSON>down<PERSON>ender<PERSON> from './MarkdownRenderer';
import FAQSchema from './FAQSchema';
import AITakeaways from './AITakeaways';

const EnhancedBlogRenderer = ({ post, showTakeaways = true }) => {
  if (!post) return null;

  // Generate post URL for schema
  const postUrl = `https://verifiedonward.com/blog/${post.slug}`;

  // Enhanced meta description with keywords
  const enhancedMetaDescription = post.metaDescription || 
    `${post.excerpt} Expert visa guidance and travel documentation tips from VerifiedOnward.`;

  // Generate keywords from post content and tags
  const generateKeywords = () => {
    const baseKeywords = ['visa application', 'travel documents', 'embassy requirements', 'flight reservation'];
    const tagKeywords = post.tags || [];
    const contentKeywords = extractKeywordsFromContent(post.content);
    
    return [...baseKeywords, ...tagKeywords, ...contentKeywords].slice(0, 15).join(', ');
  };

  // Extract keywords from content
  const extractKeywordsFromContent = (content) => {
    const keywords = [];
    const commonTerms = [
      'dummy ticket', 'flight reservation', 'visa requirements', 'embassy approval',
      'travel planning', 'document verification', 'application process', 'tourist visa',
      'business visa', 'schengen visa', 'processing time', 'approval rate'
    ];
    
    commonTerms.forEach(term => {
      if (content.toLowerCase().includes(term.toLowerCase())) {
        keywords.push(term);
      }
    });
    
    return keywords;
  };

  // Update document head with SEO meta tags
  useEffect(() => {
    // Update title
    document.title = `${post.title} | VerifiedOnward`;

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = enhancedMetaDescription;

    // Update keywords
    let metaKeywords = document.querySelector('meta[name="keywords"]');
    if (!metaKeywords) {
      metaKeywords = document.createElement('meta');
      metaKeywords.name = 'keywords';
      document.head.appendChild(metaKeywords);
    }
    metaKeywords.content = generateKeywords();

    // Add canonical link
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.rel = 'canonical';
      document.head.appendChild(canonical);
    }
    canonical.href = postUrl;

  }, [post.title, enhancedMetaDescription, postUrl]);

  return (
    <>

      {/* Blog Post Header */}
      <header className="mb-12">
        <div className="mb-6">
          <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 leading-tight mb-4">
            {post.title}
          </h1>
          
          <div className="flex items-center text-neutral-600 text-sm space-x-4 mb-6">
            <time dateTime={post.publishDate} className="bg-neutral-100 px-3 py-1 rounded-full">
              {post.publishDate}
            </time>
            <span>•</span>
            <span className="bg-brand-50 text-brand-700 px-3 py-1 rounded-full">
              {post.readTime || '5 min read'}
            </span>
          </div>

          <p className="text-xl text-neutral-700 leading-relaxed max-w-4xl">
            {post.excerpt}
          </p>
        </div>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-brand-100 text-brand-800 px-3 py-1 rounded-full text-sm font-medium"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </header>

      {/* Main Content with Enhanced Markdown Rendering */}
      <main className="mb-12">
        <article className="max-w-none">
          <MarkdownRenderer 
            content={post.content} 
            className="blog-content"
          />
        </article>

        {/* AI-Friendly Takeaways */}
        {showTakeaways && (
          <AITakeaways 
            content={post.content}
            className="mt-12"
          />
        )}
      </main>

      {/* Structured Data Schemas */}
      <FAQSchema 
        content={post.content}
        postTitle={post.title}
        postUrl={postUrl}
      />
    </>
  );
};

export default EnhancedBlogRenderer;
