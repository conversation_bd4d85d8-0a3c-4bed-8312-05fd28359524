import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';

const UnifiedCTA = () => {
  const navigate = useNavigate();
  const location = useLocation();
  return (
    <section className="py-10 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="bg-white rounded-xl shadow-lg p-8 text-center"
        >
          {/* Headline */}
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
            Need a flight reservation? Get one in 60 seconds.
          </h2>

          {/* Subheadline */}
          <p className="text-lg text-gray-600 mb-6 leading-relaxed max-w-2xl mx-auto">
            Professional visa-ready flight documents for visa applications. Real flight data, instant download, embassy-approved format.
          </p>

          {/* Trust badges */}
          <div className="flex flex-wrap justify-center gap-3 mb-8">
            <span className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-800 text-sm font-medium rounded-full">
              ✅ Instant Download
            </span>
            <span className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-800 text-sm font-medium rounded-full">
              ✅ Embassy Approved
            </span>
            <span className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-800 text-sm font-medium rounded-full">
              ✅ Real Flight Data
            </span>
          </div>

          {/* CTA Button */}
          <Link
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-lg font-semibold rounded-full hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
            {...getCTANavigationProps(navigate, location.pathname)}
          >
            Start Your Reservation Securely →
          </Link>

          {/* Trust Footer */}
          <div className="mt-6 text-sm text-gray-500 text-center">
            <p>✓ Secure payment • ✓ Instant download • Email support</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default UnifiedCTA;
