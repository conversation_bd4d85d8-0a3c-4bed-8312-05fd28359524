import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import PassengerDetailsForm from './PassengerDetailsForm';
import InlinePaymentSection from './InlinePaymentSection';
import ErrorBoundary from './ErrorBoundary';
import { useBooking } from '../context/BookingContext';
import { ticketAPI } from '../services/api';

const FlightSummaryWithPassengerForm = ({
  tripType,
  selectedOutboundFlight,
  selectedReturnFlight,
  searchData,
  onEditOutbound,
  onEditReturn,
  // Passenger form props
  passengers = [{ id: 1, firstName: '', lastName: '' }],
  email = '',
  onPassengerDetailsSubmit,
  onPassengersChange,
  onEmailChange,
  isPassengerDetailsLoading = false,
  className = "",
  // Inline payment props
  enableInlinePayment = false
}) => {
  console.log('🚀 FlightSummaryWithPassengerForm: Component rendering with props:', {
    tripType,
    selectedOutboundFlight: selectedOutboundFlight ? 'Present' : 'Missing',
    selectedReturnFlight: selectedReturnFlight ? 'Present' : 'Missing',
    enableInlinePayment,
    passengers: passengers?.length || 0,
    email
  });

  const navigate = useNavigate();
  const {
    setPassengers: setBookingPassengers,
    setEmail: setBookingEmail,
    setPaymentId,
    setBookingReference,
    setStep
  } = useBooking();

  // State for inline payment flow
  const [showPaymentSection, setShowPaymentSection] = useState(false);
  const [passengerData, setPassengerData] = useState({ passengers: [], email: '' });
  const [paymentError, setPaymentError] = useState(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Debug state after it's defined (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 FlightSummaryWithPassengerForm: Component state:', {
      showPaymentSection,
      passengerData,
      paymentError,
      isProcessingPayment,
      enableInlinePayment
    });
  }

  // Monitor showPaymentSection changes (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 FlightSummaryWithPassengerForm: showPaymentSection changed to:', showPaymentSection);
      if (showPaymentSection) {
        console.log('✅ Payment section should now be visible');
        console.log('✅ passengerData:', passengerData);
      } else {
        console.log('❌ Payment section is hidden');
      }
    }
  }, [showPaymentSection, passengerData]);

  // Ref for scrolling back to passenger details
  const passengerDetailsRef = useRef(null);
  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDuration = (duration) => {
    // Duration is in ISO 8601 format (PT6H15M)
    const match = duration.match(/PT(\d+H)?(\d+M)?/);
    if (!match) return duration;
    
    const hours = match[1] ? match[1].replace('H', 'h ') : '';
    const minutes = match[2] ? match[2].replace('M', 'm') : '';
    
    return `${hours}${minutes}`.trim();
  };

  const getStopsText = (stops) => {
    if (stops === 0) return 'Direct';
    if (stops === 1) return '1 Stop';
    return `${stops} Stops`;
  };

  // Calculate total price
  const calculateTotalPrice = () => {
    if (tripType === 'return' && selectedOutboundFlight && selectedReturnFlight) {
      return 9.98; // $4.99 + $4.99 for return flights
    }
    return 4.99; // $4.99 for one-way flights
  };

  // Handle passenger details submission for inline payment
  const handleInlinePassengerSubmit = (formData) => {
    console.log('🚀 FlightSummaryWithPassengerForm: handleInlinePassengerSubmit called');
    console.log('🚀 FlightSummaryWithPassengerForm: enableInlinePayment =', enableInlinePayment);
    console.log('🚀 FlightSummaryWithPassengerForm: formData =', formData);
    console.log('🚀 FlightSummaryWithPassengerForm: Current showPaymentSection =', showPaymentSection);

    // 🔥 BULLETPROOF STATE UPDATES
    try {
      if (enableInlinePayment) {
        console.log('✅ FlightSummaryWithPassengerForm: Using inline payment flow');

        // Enhanced validation with detailed logging
        if (!formData) {
          console.error('❌ FlightSummaryWithPassengerForm: formData is null/undefined:', formData);
          alert('Form data is missing. Please try again.');
          return;
        }

        if (!formData.passengers) {
          console.error('❌ FlightSummaryWithPassengerForm: formData.passengers is missing:', formData);
          alert('Passenger data is missing. Please try again.');
          return;
        }

        if (!Array.isArray(formData.passengers) || formData.passengers.length === 0) {
          console.error('❌ FlightSummaryWithPassengerForm: formData.passengers is not a valid array:', formData.passengers);
          alert('Invalid passenger data. Please try again.');
          return;
        }

        if (!formData.email || typeof formData.email !== 'string' || !formData.email.trim()) {
          console.error('❌ FlightSummaryWithPassengerForm: formData.email is invalid:', formData.email);
          alert('Email is required. Please try again.');
          return;
        }

        console.log('✅ FlightSummaryWithPassengerForm: Form data validation passed');
        console.log('🔄 FlightSummaryWithPassengerForm: Setting passenger data...');

        // Use React's functional state updates for reliability
        setPassengerData(prevData => {
          console.log('🔄 Previous passenger data:', prevData);
          console.log('🔄 New passenger data:', formData);
          return { ...formData }; // Create a new object to ensure state change
        });

        console.log('🔄 FlightSummaryWithPassengerForm: Setting showPaymentSection to true...');
        setShowPaymentSection(prevShow => {
          console.log('🔄 Previous showPaymentSection:', prevShow);
          console.log('🔄 New showPaymentSection: true');
          return true;
        });

        console.log('🔄 FlightSummaryWithPassengerForm: Clearing payment error...');
        setPaymentError(prevError => {
          console.log('🔄 Previous payment error:', prevError);
          console.log('🔄 New payment error: null');
          return null;
        });

        console.log('✅ FlightSummaryWithPassengerForm: State updates completed');

        // Force a re-render check with more detailed logging
        setTimeout(() => {
          console.log('🔍 FlightSummaryWithPassengerForm: Delayed state check...');
          console.log('🔍 showPaymentSection should be true');
          console.log('🔍 passengerData should contain:', formData);
        }, 100);

      } else {
        console.log('⚠️ FlightSummaryWithPassengerForm: Using old checkout navigation');
        // Use original submission logic
        if (onPassengerDetailsSubmit) {
          onPassengerDetailsSubmit(formData);
        } else {
          console.error('❌ FlightSummaryWithPassengerForm: onPassengerDetailsSubmit is not defined');
        }
      }
    } catch (error) {
      console.error('❌ FlightSummaryWithPassengerForm: Critical error in handleInlinePassengerSubmit:', error);
      console.error('❌ Stack trace:', error.stack);

      // Show user-friendly error
      alert('There was an error processing your request. Please refresh the page and try again.');

      // Fallback to old flow if available
      if (onPassengerDetailsSubmit) {
        try {
          onPassengerDetailsSubmit(formData);
        } catch (fallbackError) {
          console.error('❌ FlightSummaryWithPassengerForm: Fallback also failed:', fallbackError);
        }
      }
    }
  };

  // Handle back to passenger details
  const handleBackToPassengerDetails = () => {
    setShowPaymentSection(false);
    setPaymentError(null);

    // Scroll back to passenger details section
    setTimeout(() => {
      if (passengerDetailsRef.current) {
        passengerDetailsRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    }, 100); // Small delay to allow payment section to hide first
  };

  // 🔥 BULLETPROOF PAYMENT SUCCESS HANDLER
  const handlePaymentSuccess = async (paymentResult) => {
    console.log('🎉 Payment success received:', paymentResult);
    setIsProcessingPayment(true);
    setPaymentError(null);

    try {
      // 🔥 GENERATE BOOKING REFERENCE
      const bookingRef = generateReservationCode();

      // Save to booking context
      setBookingPassengers(passengerData.passengers);
      setBookingEmail(passengerData.email);
      setPaymentId(paymentResult.paymentId);
      setBookingReference(bookingRef);

      // 🔥 SAVE TO LOCALSTORAGE FOR CRASH RECOVERY
      const recoveryData = {
        passengers: passengerData.passengers,
        email: passengerData.email,
        paymentId: paymentResult.paymentId,
        bookingReference: bookingRef,
        searchData,
        selectedFlight: tripType === 'oneWay' ? selectedOutboundFlight : null,
        selectedOutboundFlight: tripType === 'return' ? selectedOutboundFlight : null,
        selectedReturnFlight: tripType === 'return' ? selectedReturnFlight : null,
        totalAmount: calculateTotalPrice(),
        timestamp: new Date().toISOString()
      };

      localStorage.setItem('bookingData', JSON.stringify(recoveryData));
      console.log('💾 Booking data saved to localStorage for recovery');

      // Try to create booking with backend (but don't fail if it doesn't work)
      try {
        const paymentMethod = paymentResult.paymentId?.startsWith('pi_') ? 'stripe' :
                             paymentResult.paymentId?.startsWith('paypal_') ? 'paypal' : 'stripe';

        const bookingData = {
          searchData,
          selectedFlight: tripType === 'oneWay' ? selectedOutboundFlight : null,
          selectedOutboundFlight: tripType === 'return' ? selectedOutboundFlight : null,
          selectedReturnFlight: tripType === 'return' ? selectedReturnFlight : null,
          passengers: passengerData.passengers,
          email: passengerData.email,
          paymentId: paymentResult.paymentId,
          paymentMethod: paymentMethod,
          totalAmount: calculateTotalPrice()
        };

        const response = await ticketAPI.createBooking(bookingData);
        if (response.success) {
          setBookingReference(response.bookingReference);
          console.log('✅ Backend booking created successfully');
        }
      } catch (backendError) {
        console.warn('⚠️ Backend unavailable, but payment succeeded - continuing:', backendError);
      }

      // 🔥 ALWAYS NAVIGATE TO SUCCESS - PAYMENT WAS SUCCESSFUL
      setStep('success');
      navigate('/success');

    } catch (error) {
      console.error('❌ Critical error in payment success handler:', error);

      // 🔥 EVEN IF EVERYTHING FAILS, PAYMENT WAS SUCCESSFUL
      alert('✅ Payment successful! Your verified flight reservation is ready to download.');
      navigate('/success');

    } finally {
      setIsProcessingPayment(false);
    }
  };

  // Handle payment error
  const handlePaymentError = (error) => {
    setPaymentError(error);
    setIsProcessingPayment(false);
  };

  const renderFlightDetails = (flight, title, route, date) => {
    // Safety checks for flight data
    if (!flight || typeof flight !== 'object') {
      console.warn('FlightSummaryWithPassengerForm: Invalid flight data', flight);
      return null;
    }

    const airline = flight.airline || {};
    const flightInfo = flight.flight || {};
    const departure = flightInfo.departure || {};
    const arrival = flightInfo.arrival || {};

    return (
      <div className="mb-6 last:mb-0">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
            {title}
          </h4>
          <span className="text-xs text-gray-500">
            {formatDate(date)}
          </span>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          {/* Airline Info */}
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center overflow-hidden shadow-sm">
              {airline.logo ? (
                <img
                  src={airline.logo}
                  alt={airline.name || 'Airline'}
                  className="w-6 h-6 object-contain"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
              ) : null}
              <div className="text-xs font-bold text-gray-600" style={{ display: airline.logo ? 'none' : 'block' }}>
                ✈️
              </div>
            </div>
            <div>
              <div className="font-semibold text-gray-900 text-sm">{airline.name || 'Unknown Airline'}</div>
              <div className="text-xs text-gray-500">{flightInfo.number || '--'}</div>
            </div>
          </div>

          {/* Flight Route */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {departure.time ? formatTime(departure.time) : '--:--'}
                </div>
                <div className="text-xs text-gray-500">
                  {departure.airport || '--'}
                </div>
              </div>

              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1 h-0.5 bg-blue-200"></div>
                    <div className="text-xs text-gray-500 px-2">
                      {flightInfo.duration ? formatDuration(flightInfo.duration) : '--'}
                    </div>
                    <div className="flex-1 h-0.5 bg-blue-200"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {getStopsText(flightInfo.stops || 0)}
                  </div>
                </div>
              </div>

              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {arrival.time ? formatTime(arrival.time) : '--:--'}
                </div>
                <div className="text-xs text-gray-500">
                  {arrival.airport || '--'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Don't render if no flights are selected
  const hasSelectedFlights = tripType === 'oneWay' 
    ? selectedOutboundFlight 
    : selectedOutboundFlight && selectedReturnFlight;

  if (!hasSelectedFlights) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      <div className="sticky top-8">
        {/* Flight Summary Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-6 border-b border-gray-200"
        >
          {/* Header */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Flight Summary
            </h3>
          </div>

          {/* Flight Details */}
          {tripType === 'oneWay' && selectedOutboundFlight && (
            renderFlightDetails(
              selectedOutboundFlight,
              'Departure Flight',
              `${searchData.origin} → ${searchData.destination}`,
              searchData.date
            )
          )}

          {tripType === 'return' && selectedOutboundFlight && selectedReturnFlight && (
            <>
              {renderFlightDetails(
                selectedOutboundFlight,
                'Departure Flight',
                `${searchData.origin} → ${searchData.destination}`,
                searchData.date
              )}
              {renderFlightDetails(
                selectedReturnFlight,
                'Return Flight',
                `${searchData.destination} → ${searchData.origin}`,
                searchData.returnDate
              )}
            </>
          )}

          {/* Price Summary */}
          <div className="border-t pt-4 mt-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Total Price:</span>
              <span className="price-checkout">
                ${calculateTotalPrice().toFixed(2)}
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Embassy-approved flight reservation for visa applications
            </p>
          </div>
        </motion.div>

        {/* Passenger Details Form Section */}
        <motion.div
          ref={passengerDetailsRef}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="p-6"
          data-passenger-details
        >
          {/* Payment Error Display */}
          {paymentError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg"
            >
              <p className="text-sm text-red-600">{paymentError}</p>
            </motion.div>
          )}

          <PassengerDetailsForm
            initialPassengers={passengers}
            initialEmail={email}
            onSubmit={enableInlinePayment ? handleInlinePassengerSubmit : onPassengerDetailsSubmit}
            onPassengersChange={onPassengersChange}
            onEmailChange={onEmailChange}
            isLoading={isPassengerDetailsLoading || isProcessingPayment}
            submitButtonText={enableInlinePayment ? "Continue to Payment" : "Continue to Payment"}
            disabled={showPaymentSection}
          />


        </motion.div>

        {/* Inline Payment Section */}
        {enableInlinePayment && (
          <div className="px-6 pb-6">
            <ErrorBoundary fallbackMessage="Payment system temporarily unavailable. Please try refreshing or contact support.">
              <InlinePaymentSection
                isVisible={showPaymentSection}
                onBack={handleBackToPassengerDetails}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentError={handlePaymentError}
                totalAmount={calculateTotalPrice()}
                tripType={tripType}
                passengers={passengerData.passengers}
                email={passengerData.email}
              />
            </ErrorBoundary>
          </div>
        )}
      </div>
    </div>
  );
};

console.log('🚀 FlightSummaryWithPassengerForm: Component definition complete');

export default FlightSummaryWithPassengerForm;
