import React from 'react';
import { LightBulbIcon } from '@heroicons/react/24/outline';

const AITakeaways = ({ content, className = '' }) => {
  // Generate AI-friendly takeaways from content sections
  const generateTakeaways = (content) => {
    const sections = content.split(/(?=##\s)/);
    const allTakeaways = [];
    
    sections.forEach((section, index) => {
      const lines = section.trim().split('\n');
      if (lines.length === 0) return;
      
      // Get section title
      const titleLine = lines[0];
      if (!titleLine.startsWith('##')) return;
      
      const sectionTitle = titleLine.replace(/^##\s*/, '').trim();
      
      // Skip FAQ sections and conclusion sections
      if (sectionTitle.toLowerCase().includes('faq') || 
          sectionTitle.toLowerCase().includes('conclusion') ||
          sectionTitle.toLowerCase().includes('summary')) {
        return;
      }
      
      // Extract key points from section content
      const sectionContent = lines.slice(1).join('\n');
      const takeaways = extractKeyPoints(sectionContent, sectionTitle);
      
      if (takeaways.length > 0) {
        allTakeaways.push({
          section: sectionTitle,
          points: takeaways
        });
      }
    });
    
    return allTakeaways;
  };

  // Extract key points from section content
  const extractKeyPoints = (content, sectionTitle) => {
    const points = [];
    
    // Look for existing bullet points with checkmarks
    const checkmarkPoints = content.match(/✅[^✅❌\n]+/g);
    if (checkmarkPoints) {
      checkmarkPoints.forEach(point => {
        const cleanPoint = point.replace('✅', '').trim();
        if (cleanPoint.length > 10) {
          points.push(cleanPoint);
        }
      });
    }
    
    // Look for bold statements that could be key points
    const boldStatements = content.match(/\*\*([^*]+)\*\*/g);
    if (boldStatements && points.length < 3) {
      boldStatements.forEach(statement => {
        const cleanStatement = statement.replace(/\*\*/g, '').trim();
        if (cleanStatement.length > 10 && cleanStatement.length < 100) {
          // Avoid duplicates
          if (!points.some(p => p.toLowerCase().includes(cleanStatement.toLowerCase()))) {
            points.push(cleanStatement);
          }
        }
      });
    }
    
    // Generate contextual takeaways based on section title
    if (points.length < 2) {
      const contextualPoints = generateContextualTakeaways(sectionTitle, content);
      points.push(...contextualPoints);
    }
    
    // Limit to 3-5 points per section
    return points.slice(0, 4);
  };

  // Generate contextual takeaways based on section content
  const generateContextualTakeaways = (title, content) => {
    const points = [];
    
    // Visa-related takeaways
    if (title.toLowerCase().includes('visa') || title.toLowerCase().includes('application')) {
      if (content.includes('embassy') || content.includes('consulate')) {
        points.push('Embassy requirements vary by country and visa type');
      }
      if (content.includes('document') || content.includes('proof')) {
        points.push('Proper documentation is essential for visa approval');
      }
      if (content.includes('processing') || content.includes('time')) {
        points.push('Allow sufficient processing time for visa applications');
      }
    }
    
    // Flight reservation takeaways
    if (title.toLowerCase().includes('flight') || title.toLowerCase().includes('ticket')) {
      if (content.includes('dummy') || content.includes('reservation')) {
        points.push('Flight reservations provide proof without financial risk');
      }
      if (content.includes('real') || content.includes('actual')) {
        points.push('Real tickets carry higher financial risk if visa is denied');
      }
      if (content.includes('embassy') || content.includes('accept')) {
        points.push('Embassy-approved reservations meet official requirements');
      }
    }
    
    // Requirements and tips takeaways
    if (title.toLowerCase().includes('requirement') || title.toLowerCase().includes('tip')) {
      points.push('Following official guidelines increases approval chances');
      if (content.includes('mistake') || content.includes('error')) {
        points.push('Avoiding common mistakes prevents visa rejections');
      }
    }
    
    return points.slice(0, 2);
  };

  const takeaways = generateTakeaways(content);
  
  // Don't render if no takeaways generated
  if (takeaways.length === 0) {
    return null;
  }

  return (
    <div className={`ai-takeaways bg-gradient-to-r from-brand-50/40 to-accent-50/30 border border-brand-200/50 rounded-xl p-6 my-8 ${className}`}>
      <div className="flex items-center mb-4">
        <LightBulbIcon className="w-6 h-6 text-brand-600 mr-3" />
        <h4 className="text-lg font-semibold text-neutral-800">Key Takeaways</h4>
      </div>
      
      <div className="space-y-6">
        {takeaways.map((section, index) => (
          <div key={index}>
            <h5 className="text-base font-medium text-neutral-700 mb-3">{section.section}:</h5>
            <ul className="space-y-2">
              {section.points.map((point, pointIndex) => (
                <li key={pointIndex} className="flex items-start space-x-3">
                  <span className="text-brand-600 mt-1.5 w-1.5 h-1.5 bg-brand-600 rounded-full flex-shrink-0"></span>
                  <span className="text-neutral-700 text-sm leading-relaxed">{point}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      
      {/* AI-friendly structured data for better extraction */}
      <div className="hidden" itemScope itemType="https://schema.org/ItemList">
        <meta itemProp="name" content="Key Takeaways" />
        {takeaways.map((section, index) => (
          <div key={index} itemScope itemType="https://schema.org/ListItem" itemProp="itemListElement">
            <meta itemProp="position" content={index + 1} />
            <div itemProp="item" itemScope itemType="https://schema.org/Thing">
              <meta itemProp="name" content={section.section} />
              <meta itemProp="description" content={section.points.join('. ')} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AITakeaways;
