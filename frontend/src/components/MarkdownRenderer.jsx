import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeSanitize from 'rehype-sanitize';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

const MarkdownRenderer = ({ content, className = '' }) => {
  if (!content) return null;

  // Clean content by removing stray # symbols and fixing formatting
  const cleanContent = (rawContent) => {
    return rawContent
      // Remove standalone # symbols that aren't part of headings
      .replace(/^#(?!\s)/gm, '')
      .replace(/(?<!^|\n)#(?!\s)/g, '')
      // Fix spacing around headings
      .replace(/^(#{1,6})\s*(.+)$/gm, '$1 $2')
      // Ensure proper line breaks after bold text
      .replace(/\*\*([^*]+)\*\*(?!\s*\n)/g, '**$1**  ')
      // Clean up multiple consecutive line breaks
      .replace(/\n{3,}/g, '\n\n')
      // Remove trailing spaces at end of lines (except intentional markdown line breaks)
      .replace(/[ \t]+$/gm, '')
      .trim();
  };

  // Custom components for consistent styling with the existing design
  const components = {
    // Headings with proper SEO structure
    h1: ({ children }) => (
      <h1 className="text-3xl md:text-4xl font-bold text-neutral-800 mb-6 leading-tight">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-2xl md:text-3xl font-bold text-neutral-800 mb-6 leading-tight mt-12 first:mt-0">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-xl font-semibold text-neutral-800 mb-4 mt-8">
        {children}
      </h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-lg font-semibold text-neutral-800 mb-3 mt-6">
        {children}
      </h4>
    ),
    h5: ({ children }) => (
      <h5 className="text-base font-semibold text-neutral-800 mb-2 mt-4">
        {children}
      </h5>
    ),
    h6: ({ children }) => (
      <h6 className="text-sm font-semibold text-neutral-800 mb-2 mt-4">
        {children}
      </h6>
    ),

    // Paragraphs with enhanced styling
    p: ({ children }) => (
      <p className="text-neutral-700 leading-relaxed mb-6" style={{ fontSize: '18px', lineHeight: '1.7' }}>
        {children}
      </p>
    ),

    // Strong/Bold text with enhanced visibility
    strong: ({ children }) => (
      <strong className="font-bold text-neutral-900 bg-brand-50/30 px-1 py-0.5 rounded">
        {children}
      </strong>
    ),

    // Emphasis/Italic text
    em: ({ children }) => (
      <em className="italic text-neutral-700">
        {children}
      </em>
    ),

    // Links with brand styling
    a: ({ href, children }) => (
      <a
        href={href}
        className="text-brand-600 hover:text-brand-700 underline decoration-brand-300 hover:decoration-brand-500 transition-colors duration-200"
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),

    // Enhanced Lists with better styling
    ul: ({ children }) => (
      <ul className="space-y-3 mb-6">
        {children}
      </ul>
    ),

    // Ordered lists
    ol: ({ children }) => (
      <ol className="space-y-3 mb-6">
        {children}
      </ol>
    ),

    // List items with special handling for checkmarks
    li: ({ children }) => {
      // Check if this is a checkmark or X mark list item
      const childText = typeof children === 'string' ? children :
        (Array.isArray(children) ? children.join('') : '');

      if (childText.startsWith('✅')) {
        return (
          <li className="flex items-start space-x-3 text-neutral-700" style={{ fontSize: '18px', lineHeight: '1.7' }}>
            <span className="text-green-600 text-lg mt-0.5">✅</span>
            <span>{childText.replace('✅', '').trim()}</span>
          </li>
        );
      } else if (childText.startsWith('❌')) {
        return (
          <li className="flex items-start space-x-3 text-neutral-700" style={{ fontSize: '18px', lineHeight: '1.7' }}>
            <span className="text-red-600 text-lg mt-0.5">❌</span>
            <span>{childText.replace('❌', '').trim()}</span>
          </li>
        );
      } else {
        return (
          <li className="flex items-start space-x-3 text-neutral-700" style={{ fontSize: '18px', lineHeight: '1.7' }}>
            <span className="text-brand-600 mt-2 w-2 h-2 bg-brand-600 rounded-full flex-shrink-0"></span>
            <span>{children}</span>
          </li>
        );
      }
    },

    // Blockquotes
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-brand-300 pl-6 py-2 mb-4 bg-brand-50/30 rounded-r-lg">
        <div className="text-neutral-700 italic">
          {children}
        </div>
      </blockquote>
    ),

    // Code blocks
    code: ({ inline, children }) => {
      if (inline) {
        return (
          <code className="bg-neutral-100 text-neutral-800 px-2 py-1 rounded text-sm font-mono">
            {children}
          </code>
        );
      }
      return (
        <pre className="bg-neutral-100 p-4 rounded-lg mb-4 overflow-x-auto">
          <code className="text-neutral-800 text-sm font-mono">
            {children}
          </code>
        </pre>
      );
    },

    // Horizontal rules
    hr: () => (
      <hr className="border-neutral-200 my-8" />
    ),

    // Tables
    table: ({ children }) => (
      <div className="overflow-x-auto mb-4">
        <table className="min-w-full border-collapse border border-neutral-200">
          {children}
        </table>
      </div>
    ),
    thead: ({ children }) => (
      <thead className="bg-neutral-50">
        {children}
      </thead>
    ),
    tbody: ({ children }) => (
      <tbody>
        {children}
      </tbody>
    ),
    tr: ({ children }) => (
      <tr className="border-b border-neutral-200">
        {children}
      </tr>
    ),
    th: ({ children }) => (
      <th className="border border-neutral-200 px-4 py-2 text-left font-semibold text-neutral-800">
        {children}
      </th>
    ),
    td: ({ children }) => (
      <td className="border border-neutral-200 px-6 py-3 text-neutral-700">
        {children}
      </td>
    ),
  };

  const processedContent = cleanContent(content);

  return (
    <div className={`markdown-content prose prose-lg max-w-none ${className}`}>
      <ReactMarkdown
        components={components}
        rehypePlugins={[rehypeRaw, rehypeSanitize]}
        remarkPlugins={[remarkGfm]}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
