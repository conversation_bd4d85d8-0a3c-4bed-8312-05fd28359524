import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getCTANavigationProps } from '../utils/ctaNavigation';
import {
  CheckIcon,
  ShieldCheckIcon,
  ClockIcon,
  DocumentCheckIcon,
  StarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/solid';

const BlogCTA = () => {
  const navigate = useNavigate();
  const location = useLocation();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="premium-card bg-gradient-to-br from-brand-50/80 to-accent-50/60 backdrop-blur-sm my-12 relative overflow-hidden"
    >
      {/* Premium background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-brand-400/10 to-accent-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-accent-400/10 to-brand-400/10 rounded-full blur-2xl"></div>

      <div className="relative z-10 p-8 text-center">
        {/* Removed aggressive urgency indicator */}

        <h3 className="heading-secondary text-neutral-900 mb-4">
          Ready to Apply for Your Visa?
        </h3>

        <p className="text-body text-neutral-600 mb-6 max-w-2xl mx-auto">
          Get your professionally formatted flight reservation in 60 seconds. Professional format, real flight data, instant download.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="trust-badge bg-white/80 backdrop-blur-sm">
            <ClockIcon className="w-4 h-4 mr-2 text-brand-500" />
            60-Second Delivery
          </div>
          <div className="trust-badge bg-white/80 backdrop-blur-sm">
            <ShieldCheckIcon className="w-4 h-4 mr-2 text-green-500" />
            Visa-Ready
          </div>
          <div className="trust-badge bg-white/80 backdrop-blur-sm">
            <DocumentCheckIcon className="w-4 h-4 mr-2 text-accent-500" />
            Real Flight Data
          </div>
        </div>

        <Link
          className="premium-button-large group"
          {...getCTANavigationProps(navigate, location.pathname)}
        >
          Start Your Reservation Securely
          <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Link>

        {/* Simplified social proof */}
        <div className="mt-6 text-sm text-neutral-500">
          <p>✓ Embassy-approved format • ✓ Professional quality</p>
        </div>
      </div>
    </motion.div>
  );
};

export default BlogCTA;
