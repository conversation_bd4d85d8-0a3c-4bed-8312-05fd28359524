{"name": "verifiedonward-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "axios": "^1.10.0", "framer-motion": "^12.23.0", "puppeteer": "^24.14.0", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.3", "react-toastify": "^11.0.5", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.0"}}